import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  CheckCircle, 
  Clock, 
  Download, 
  RotateCcw, 
  X, 
  User, 
  Film, 
  Image as ImageIcon,
  Sparkles,
  Eye
} from "lucide-react";

interface GenerationProgress {
  stage: string;
  title: string;
  description: string;
  data?: any;
  completed: boolean;
}

interface IllustrationResponse {
  success: boolean;
  message: string;
  final_illustration?: string;
  processed_story?: string;
  characters?: any[];
  storyboards?: any[];
  generated_images?: any[];
  error?: string;
}

interface IllustrationGeneratorProps {
  isGenerating: boolean;
  progress: GenerationProgress[];
  result: IllustrationResponse | null;
  onCancel: () => void;
  onReset: () => void;
}

export function IllustrationGenerator({ 
  isGenerating, 
  progress, 
  result, 
  onCancel, 
  onReset 
}: IllustrationGeneratorProps) {
  const [selectedTab, setSelectedTab] = useState("progress");
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const downloadImage = (imageUrl: string, filename: string) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const openImageModal = (imageUrl: string) => {
    setSelectedImage(imageUrl);
  };

  const closeImageModal = () => {
    setSelectedImage(null);
  };

  return (
    <div className="flex flex-col h-screen p-6">
      {/* 顶部控制栏 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full">
            <Sparkles className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-white">插画生成中</h1>
            <p className="text-gray-300 text-sm">
              {isGenerating ? "正在为您生成精美插画..." : "生成完成"}
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          {isGenerating && (
            <Button variant="outline" onClick={onCancel} className="border-red-500/50 text-red-400 hover:bg-red-500/10">
              <X className="w-4 h-4 mr-2" />
              取消
            </Button>
          )}
          <Button variant="outline" onClick={onReset} className="border-white/30 text-white hover:bg-white/10">
            <RotateCcw className="w-4 h-4 mr-2" />
            重新开始
          </Button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-4 bg-white/10 border-white/20">
            <TabsTrigger value="progress" className="data-[state=active]:bg-white/20">
              <Clock className="w-4 h-4 mr-2" />
              生成进度
            </TabsTrigger>
            <TabsTrigger value="story" className="data-[state=active]:bg-white/20" disabled={!result}>
              <User className="w-4 h-4 mr-2" />
              故事与角色
            </TabsTrigger>
            <TabsTrigger value="storyboard" className="data-[state=active]:bg-white/20" disabled={!result}>
              <Film className="w-4 h-4 mr-2" />
              分镜设计
            </TabsTrigger>
            <TabsTrigger value="images" className="data-[state=active]:bg-white/20" disabled={!result}>
              <ImageIcon className="w-4 h-4 mr-2" />
              生成图片
            </TabsTrigger>
          </TabsList>

          {/* 生成进度 */}
          <TabsContent value="progress" className="flex-1 mt-4">
            <Card className="h-full bg-white/10 backdrop-blur-sm border-white/20">
              <CardHeader>
                <CardTitle className="text-white">生成进度</CardTitle>
                <CardDescription className="text-gray-300">
                  实时显示插画生成的各个阶段
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[500px]">
                  <div className="space-y-4">
                    {progress.map((stage, index) => (
                      <div key={index} className="flex items-start gap-4 p-4 bg-white/5 rounded-lg border border-white/10">
                        <div className="flex-shrink-0 mt-1">
                          {stage.completed ? (
                            <CheckCircle className="w-6 h-6 text-green-400" />
                          ) : (
                            <div className="w-6 h-6 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />
                          )}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium text-white mb-1">{stage.title}</h3>
                          <p className="text-sm text-gray-300">{stage.description}</p>
                          {stage.data && (
                            <div className="mt-2 p-2 bg-white/5 rounded text-xs text-gray-400">
                              {JSON.stringify(stage.data, null, 2)}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                    
                    {progress.length === 0 && !isGenerating && (
                      <div className="text-center py-12 text-gray-400">
                        <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p>等待开始生成...</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 故事与角色 */}
          <TabsContent value="story" className="flex-1 mt-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 h-full">
              {/* 处理后的故事 */}
              <Card className="bg-white/10 backdrop-blur-sm border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">处理后的故事</CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[400px]">
                    <p className="text-gray-200 leading-relaxed">
                      {result?.processed_story || "暂无故事内容"}
                    </p>
                  </ScrollArea>
                </CardContent>
              </Card>

              {/* 角色信息 */}
              <Card className="bg-white/10 backdrop-blur-sm border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">角色信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[400px]">
                    <div className="space-y-4">
                      {result?.characters?.map((character, index) => (
                        <div key={index} className="p-4 bg-white/5 rounded-lg border border-white/10">
                          <div className="flex gap-4">
                            {/* 角色图片 */}
                            <div className="flex-shrink-0">
                              {character.base_image_url ? (
                                <div className="relative group cursor-pointer" onClick={() => openImageModal(character.base_image_url)}>
                                  <img
                                    src={character.base_image_url}
                                    alt={character.name}
                                    className="w-20 h-20 object-cover rounded-lg border border-white/20 hover:border-white/40 transition-colors"
                                  />
                                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 rounded-lg transition-colors flex items-center justify-center">
                                    <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                                      </svg>
                                    </div>
                                  </div>
                                </div>
                              ) : (
                                <div className="w-20 h-20 bg-white/10 rounded-lg border border-white/20 flex items-center justify-center">
                                  <User className="w-8 h-8 text-gray-400" />
                                </div>
                              )}
                            </div>

                            {/* 角色信息 */}
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <h3 className="font-medium text-white">{character.name}</h3>
                                <Badge variant={character.role === 'main' ? 'default' : 'secondary'}>
                                  {character.role === 'main' ? '主角' : '配角'}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-300 mb-2">{character.description}</p>
                              <p className="text-xs text-gray-400">外貌: {character.appearance}</p>
                            </div>
                          </div>
                        </div>
                      )) || (
                        <div className="text-center py-8 text-gray-400">
                          <User className="w-8 h-8 mx-auto mb-2 opacity-50" />
                          <p>暂无角色信息</p>
                        </div>
                      )}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>

              {/* 角色基准图 */}
              {result?.characters?.some(char => char.base_image_url) && (
                <Card className="bg-white/10 backdrop-blur-sm border-white/20">
                  <CardHeader>
                    <CardTitle className="text-white">角色基准图</CardTitle>
                    <CardDescription className="text-gray-300">
                      生成的角色参考图片
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                      {result.characters
                        .filter(char => char.base_image_url)
                        .map((character, index) => (
                          <div key={index} className="space-y-2">
                            <div className="relative group cursor-pointer" onClick={() => openImageModal(character.base_image_url)}>
                              <img
                                src={character.base_image_url}
                                alt={character.name}
                                className="w-full aspect-[3/4] object-cover rounded-lg border border-white/20 hover:border-white/40 transition-colors"
                              />
                              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 rounded-lg transition-colors flex items-center justify-center">
                                <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                                  </svg>
                                </div>
                              </div>
                            </div>
                            <div className="text-center">
                              <p className="text-sm font-medium text-white">{character.name}</p>
                              <Badge variant={character.role === 'main' ? 'default' : 'secondary'} className="text-xs">
                                {character.role === 'main' ? '主角' : '配角'}
                              </Badge>
                            </div>
                          </div>
                        ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {/* 分镜设计 */}
          <TabsContent value="storyboard" className="flex-1 mt-4">
            <Card className="h-full bg-white/10 backdrop-blur-sm border-white/20">
              <CardHeader>
                <CardTitle className="text-white">分镜设计</CardTitle>
                <CardDescription className="text-gray-300">
                  详细的分镜描述和场景设计
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[500px]">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {result?.storyboards?.map((storyboard, index) => (
                      <div key={index} className="p-4 bg-white/5 rounded-lg border border-white/10">
                        <div className="flex items-center gap-2 mb-3">
                          <Badge variant="outline" className="border-blue-400 text-blue-400">
                            分镜 {storyboard.panel_id}
                          </Badge>
                        </div>
                        <div className="space-y-2 text-sm">
                          <div>
                            <span className="text-gray-400">场景: </span>
                            <span className="text-gray-200">{storyboard.scene_description}</span>
                          </div>
                          <div>
                            <span className="text-gray-400">环境: </span>
                            <span className="text-gray-200">{storyboard.environment}</span>
                          </div>
                          <div>
                            <span className="text-gray-400">动作: </span>
                            <span className="text-gray-200">{storyboard.action}</span>
                          </div>
                          <div>
                            <span className="text-gray-400">氛围: </span>
                            <span className="text-gray-200">{storyboard.mood}</span>
                          </div>
                          <div>
                            <span className="text-gray-400">镜头: </span>
                            <span className="text-gray-200">{storyboard.camera_angle}</span>
                          </div>
                        </div>
                      </div>
                    )) || (
                      <div className="col-span-2 text-center py-12 text-gray-400">
                        <Film className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p>暂无分镜信息</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 生成图片 */}
          <TabsContent value="images" className="flex-1 mt-4">
            <Card className="h-full bg-white/10 backdrop-blur-sm border-white/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center justify-between">
                  生成的图片
                  {result?.final_illustration && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => downloadImage(result.final_illustration!, 'final_illustration.png')}
                      className="border-green-500/50 text-green-400 hover:bg-green-500/10"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      下载最终插画
                    </Button>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[500px]">
                  {/* 最终插画 */}
                  {result?.final_illustration && (
                    <div className="mb-6">
                      <h3 className="text-lg font-medium text-white mb-3">最终插画</h3>
                      <div className="relative group cursor-pointer" onClick={() => openImageModal(result.final_illustration!)}>
                        <img
                          src={result.final_illustration}
                          alt="最终插画"
                          className="w-full max-w-2xl mx-auto rounded-lg border border-white/20 hover:border-white/40 transition-colors"
                        />
                        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                          <Eye className="w-8 h-8 text-white" />
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 分镜图片 */}
                  {result?.generated_images && result.generated_images.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium text-white mb-3">分镜图片</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {result.generated_images.map((image, index) => (
                          <div key={index} className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Badge variant="outline" className="border-purple-400 text-purple-400">
                                分镜 {image.panel_id}
                              </Badge>
                              <Badge variant={image.success ? "default" : "destructive"}>
                                {image.success ? "成功" : "失败"}
                              </Badge>
                            </div>
                            {image.success && image.image_url ? (
                              <div className="relative group cursor-pointer" onClick={() => openImageModal(image.image_url)}>
                                <img
                                  src={image.image_url}
                                  alt={`分镜 ${image.panel_id}`}
                                  className="w-full aspect-video object-cover rounded border border-white/20 hover:border-white/40 transition-colors"
                                />
                                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded flex items-center justify-center">
                                  <Eye className="w-6 h-6 text-white" />
                                </div>
                              </div>
                            ) : (
                              <div className="w-full aspect-video bg-red-900/20 border border-red-500/30 rounded flex items-center justify-center">
                                <p className="text-red-400 text-sm">生成失败</p>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {(!result?.generated_images || result.generated_images.length === 0) && !result?.final_illustration && (
                    <div className="text-center py-12 text-gray-400">
                      <ImageIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p>暂无生成的图片</p>
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* 图片预览模态框 */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4" onClick={closeImageModal}>
          <div className="relative max-w-4xl max-h-full">
            <img
              src={selectedImage}
              alt="预览"
              className="max-w-full max-h-full object-contain rounded-lg"
              onClick={(e) => e.stopPropagation()}
            />
            <Button
              variant="outline"
              size="sm"
              className="absolute top-4 right-4 bg-black/50 border-white/30"
              onClick={closeImageModal}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
