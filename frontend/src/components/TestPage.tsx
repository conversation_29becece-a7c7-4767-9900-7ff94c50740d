import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export function TestPage() {
  const [testResult, setTestResult] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  const testHealthCheck = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("http://localhost:8080/api/health");
      const data = await response.json();
      setTestResult(`健康检查成功: ${JSON.stringify(data, null, 2)}`);
    } catch (error) {
      setTestResult(`健康检查失败: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testStylesAPI = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("http://localhost:8080/api/styles");
      const data = await response.json();
      setTestResult(`风格列表: ${JSON.stringify(data, null, 2)}`);
    } catch (error) {
      setTestResult(`获取风格列表失败: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testIllustrationAPI = async () => {
    setIsLoading(true);
    try {
      const testData = {
        user_input: "小猫咪在花园里玩耍",
        style_preference: "anime",
        num_panels: 2
      };

      const response = await fetch("http://localhost:8080/api/generate-illustration", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData),
      });

      const data = await response.json();
      setTestResult(`插画生成结果: ${JSON.stringify(data, null, 2)}`);
    } catch (error) {
      setTestResult(`插画生成失败: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <Card className="bg-white/10 backdrop-blur-sm border-white/20">
        <CardHeader>
          <CardTitle className="text-white">API测试页面</CardTitle>
          <CardDescription className="text-gray-300">
            测试后端API连接和功能
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button 
              onClick={testHealthCheck} 
              disabled={isLoading}
              variant="outline"
              className="border-green-500/50 text-green-400 hover:bg-green-500/10"
            >
              测试健康检查
            </Button>
            <Button 
              onClick={testStylesAPI} 
              disabled={isLoading}
              variant="outline"
              className="border-blue-500/50 text-blue-400 hover:bg-blue-500/10"
            >
              测试风格API
            </Button>
            <Button 
              onClick={testIllustrationAPI} 
              disabled={isLoading}
              variant="outline"
              className="border-purple-500/50 text-purple-400 hover:bg-purple-500/10"
            >
              测试插画生成
            </Button>
          </div>
          
          {testResult && (
            <div className="mt-4 p-4 bg-black/30 rounded-lg border border-white/20">
              <h3 className="text-white font-medium mb-2">测试结果:</h3>
              <pre className="text-gray-300 text-sm overflow-auto max-h-96">
                {testResult}
              </pre>
            </div>
          )}
          
          {isLoading && (
            <div className="flex items-center gap-2 text-blue-400">
              <div className="w-4 h-4 border-2 border-blue-400/30 border-t-blue-400 rounded-full animate-spin" />
              <span>测试中...</span>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
