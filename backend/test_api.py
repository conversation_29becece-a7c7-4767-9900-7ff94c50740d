#!/usr/bin/env python3
"""
测试API端点
"""
import requests
import json

def test_api():
    """测试API端点"""
    try:
        print('🔄 测试API端点...')
        
        # 测试数据
        test_data = {
            "user_input": "小猫玩耍",
            "style_preference": "anime",
            "num_panels": 2
        }
        
        # 发送请求
        response = requests.post(
            'http://localhost:8080/api/generate-illustration',
            json=test_data,
            timeout=10
        )
        
        print(f'📊 状态码: {response.status_code}')
        
        if response.status_code == 200:
            result = response.json()
            print('✅ API调用成功！')
            print(f'📋 响应键: {list(result.keys())}')
            
            if 'data' in result and result['data']:
                data = result['data']
                print(f'📊 数据键: {list(data.keys())}')
                
                # 检查角色信息
                if 'characters' in data:
                    characters = data['characters']
                    print(f'👥 角色数量: {len(characters)}')
                    for i, char in enumerate(characters):
                        print(f'  {i+1}. {char.get("name", "未知")} - {char.get("role", "未知角色")}')
                        if char.get('base_image_url'):
                            print(f'     图片: {char["base_image_url"]}')
                        else:
                            print(f'     图片: 无')
            
        else:
            print(f'❌ API调用失败: {response.status_code}')
            print(f'响应: {response.text}')
            
    except Exception as e:
        print(f'❌ 测试失败: {e}')

if __name__ == "__main__":
    test_api()
