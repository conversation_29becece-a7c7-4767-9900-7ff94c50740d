#!/usr/bin/env python3
"""
测试LangGraph状态传递机制
"""
import asyncio
import sys
import os

# 添加src目录到路径
sys.path.append('src')

async def test_langgraph_state():
    """测试LangGraph状态传递机制"""
    try:
        from agent.illustration_graph import illustration_graph
        from langchain_core.runnables import RunnableConfig
        
        print('🔄 测试LangGraph状态传递机制...')
        
        # 收集进度更新
        progress_updates = []
        
        def progress_callback(node_name, status, data=None):
            update = f'{node_name} -> {status}'
            print(f'📊 进度: {update}')
            progress_updates.append((node_name, status))
        
        # 创建初始状态
        initial_state = {
            "user_input": "小猫玩耍",
            "style_preference": "anime",
            "num_panels": 1,
            "messages": [],
            "story_segments": [],
            "storyboards": [],
            "characters": [],
            "character_base_images": [],
            "scene_prompts": [],
            "generated_images": [],
            "main_character": None,
            "final_illustration": None,
            "processed_story": "",
            "story_type": "",
            "reasoning_model": "qwen-max-0403",
            "progress_callback": progress_callback
        }
        
        print(f'📍 初始状态中的progress_callback: {initial_state.get("progress_callback")}')
        
        # 创建配置
        config = RunnableConfig()
        
        # 执行第一个节点
        print('\n📍 手动执行第一个节点...')
        from agent.illustration_nodes import input_handler
        
        result1 = input_handler(initial_state, config)
        print(f'✅ input_handler完成')
        print(f'🔍 返回结果中的progress_callback: {result1.get("progress_callback")}')
        
        # 更新状态
        updated_state = {**initial_state, **result1}
        print(f'🔍 更新后状态中的progress_callback: {updated_state.get("progress_callback")}')
        
        # 执行第二个节点
        print('\n📍 手动执行第二个节点...')
        from agent.illustration_nodes import story_splitter
        
        result2 = story_splitter(updated_state, config)
        print(f'✅ story_splitter完成')
        print(f'🔍 返回结果中的progress_callback: {result2.get("progress_callback")}')
        
        print(f'\n📈 总共收到 {len(progress_updates)} 个进度更新:')
        for i, (node, status) in enumerate(progress_updates):
            print(f'  {i+1}. {node} -> {status}')
        
        # 现在测试LangGraph的执行
        print('\n🔄 测试LangGraph执行...')
        progress_updates.clear()  # 清空之前的更新
        
        # 使用LangGraph执行（只执行前两个节点）
        try:
            # 设置较短的超时
            result = await asyncio.wait_for(
                illustration_graph.ainvoke(initial_state, config=config),
                timeout=30
            )
            print(f'✅ LangGraph执行完成')
        except asyncio.TimeoutError:
            print('⏰ LangGraph执行超时')
        
        print(f'📈 LangGraph执行收到 {len(progress_updates)} 个进度更新:')
        for i, (node, status) in enumerate(progress_updates):
            print(f'  {i+1}. {node} -> {status}')
            
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_langgraph_state())
