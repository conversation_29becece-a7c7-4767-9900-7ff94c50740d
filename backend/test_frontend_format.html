<!DOCTYPE html>
<html>
<head>
    <title>API格式测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .data { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        button { padding: 10px 20px; margin: 10px; }
    </style>
</head>
<body>
    <h1>插画生成API格式测试</h1>
    
    <button onclick="testAPI()">测试API调用</button>
    <button onclick="clearResults()">清除结果</button>
    
    <div id="results"></div>

    <script>
        async function testAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>正在调用API...</p>';
            
            try {
                const response = await fetch('http://localhost:8080/api/generate-illustration', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_input: '小猫咪在花园里玩耍',
                        style_preference: 'anime',
                        num_panels: 2
                    })
                });
                
                const result = await response.json();
                
                // 模拟前端组件的数据处理逻辑
                const processedResult = {
                    success: result.success,
                    message: result.message,
                    final_illustration: result.data?.final_illustration,
                    processed_story: result.data?.processed_story,
                    characters: result.data?.characters || [],
                    storyboards: result.data?.storyboards || [],
                    generated_images: result.data?.generated_images || [],
                    error: result.error
                };
                
                displayResults(result, processedResult);
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
            }
        }
        
        function displayResults(originalResult, processedResult) {
            const resultsDiv = document.getElementById('results');
            
            let html = `
                <div class="section">
                    <h3>原始API响应</h3>
                    <div class="data">
                        <p><strong>Success:</strong> ${originalResult.success}</p>
                        <p><strong>Message:</strong> ${originalResult.message}</p>
                        <p><strong>Has Data:</strong> ${originalResult.data ? 'Yes' : 'No'}</p>
                        ${originalResult.error ? `<p class="error"><strong>Error:</strong> ${originalResult.error}</p>` : ''}
                    </div>
                </div>
                
                <div class="section">
                    <h3>前端处理后的数据</h3>
                    <div class="data">
                        <p><strong>Success:</strong> ${processedResult.success}</p>
                        <p><strong>Story Available:</strong> ${processedResult.processed_story ? 'Yes' : 'No'}</p>
                        <p><strong>Characters:</strong> ${processedResult.characters.length} 个</p>
                        <p><strong>Storyboards:</strong> ${processedResult.storyboards.length} 个</p>
                        <p><strong>Images:</strong> ${processedResult.generated_images.length} 个</p>
                        <p><strong>Final Illustration:</strong> ${processedResult.final_illustration ? 'Yes' : 'No'}</p>
                    </div>
                </div>
            `;
            
            // 显示角色信息
            if (processedResult.characters.length > 0) {
                html += `
                    <div class="section">
                        <h3>角色信息 (${processedResult.characters.length})</h3>
                        <div class="data">
                `;
                processedResult.characters.forEach((char, index) => {
                    html += `<p>${index + 1}. <strong>${char.name || 'N/A'}:</strong> ${char.description || 'N/A'}</p>`;
                });
                html += '</div></div>';
            }
            
            // 显示分镜信息
            if (processedResult.storyboards.length > 0) {
                html += `
                    <div class="section">
                        <h3>分镜信息 (${processedResult.storyboards.length})</h3>
                        <div class="data">
                `;
                processedResult.storyboards.forEach((board, index) => {
                    html += `<p>${index + 1}. <strong>Panel ${board.panel_id}:</strong> ${board.scene_description || 'N/A'}</p>`;
                });
                html += '</div></div>';
            }
            
            // 显示图片信息
            if (processedResult.generated_images.length > 0) {
                html += `
                    <div class="section">
                        <h3>生成图片 (${processedResult.generated_images.length})</h3>
                        <div class="data">
                `;
                processedResult.generated_images.forEach((img, index) => {
                    const status = img.success ? '✅成功' : '❌失败';
                    html += `<p>${index + 1}. <strong>Panel ${img.panel_id}:</strong> ${status}</p>`;
                    if (img.image_url) {
                        html += `<img src="${img.image_url}" alt="Panel ${img.panel_id}" style="max-width: 200px; margin: 5px;">`;
                    }
                });
                html += '</div></div>';
            }
            
            resultsDiv.innerHTML = html;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>