#!/usr/bin/env python3
"""测试真实工作流是否能正常运行"""

import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def test_workflow():
    try:
        from agent.illustration_graph import generate_illustration_sync
        
        print("🧪 开始测试真实工作流...")
        
        result = generate_illustration_sync(
            user_input='小猫在花园里玩耍',
            style_preference='anime',
            num_panels=2
        )
        
        print('✅ 工作流调用成功')
        print(f'结果类型: {type(result)}')
        
        if isinstance(result, dict):
            print(f'结果键: {list(result.keys())}')
            print(f'是否有错误: {"error" in result}')
            if 'error' in result:
                print(f'错误信息: {result["error"]}')
            else:
                print(f'processed_story: {result.get("processed_story", "未找到")}')
                print(f'characters: {len(result.get("characters", []))} 个角色')
                print(f'storyboards: {len(result.get("storyboards", []))} 个分镜')
        else:
            print(f'结果不是字典类型: {result}')
            
    except Exception as e:
        print(f'❌ 工作流调用失败: {str(e)}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_workflow()