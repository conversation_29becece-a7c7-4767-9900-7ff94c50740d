#!/usr/bin/env python3
"""
测试真实的图像生成API调用
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加src目录到Python路径
backend_dir = Path(__file__).parent
src_dir = backend_dir / "src"
sys.path.insert(0, str(src_dir))

async def test_real_image_generation():
    """测试真实的图像生成"""
    print("🎨 测试真实的图像生成API...")
    
    try:
        from agent.async_image_manager import async_image_manager
        from agent.remote_image_api import ModelType
        
        print("✅ 成功导入异步图像管理器")
        
        # 测试角色图片生成
        print("\n📋 测试角色图片生成...")
        test_characters = [
            {
                "name": "小红帽",
                "appearance": "可爱的女孩，红色帽子，黄色头发",
                "role": "main"
            }
        ]
        
        def progress_callback(message: str):
            print(f"  📊 进度: {message}")
        
        character_images = await async_image_manager.generate_character_images(
            test_characters,
            "anime",
            progress_callback
        )
        
        print(f"✅ 角色图片生成完成: {len(character_images)} 张")
        for name, url in character_images.items():
            print(f"  - {name}: {url[:50]}..." if len(url) > 50 else f"  - {name}: {url}")
        
        # 测试场景图片生成
        print("\n🎬 测试场景图片生成...")
        test_scene_prompts = [
            {
                "panel_id": 1,
                "prompt": "小红帽在森林里走路, anime style, high quality",
                "generation_type": "text2img"
            }
        ]
        
        scene_images = await async_image_manager.generate_scene_images(
            test_scene_prompts,
            character_images,
            "anime",
            progress_callback
        )
        
        print(f"✅ 场景图片生成完成: {len(scene_images)} 张")
        for img in scene_images:
            status = "✅" if img["success"] else "❌"
            print(f"  {status} 分镜{img['panel_id']}: {img['image_url'][:50] if img['image_url'] else img['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_remote_api_basic():
    """测试远程API基本连接"""
    print("\n🔗 测试远程API基本连接...")
    
    try:
        from agent.remote_image_api import RemoteImageAPI, ModelType
        
        api = RemoteImageAPI()
        print(f"✅ API客户端创建成功")
        print(f"  - 基础URL: {api.base_url}")
        print(f"  - API密钥: {api.api_key[:10]}...")
        print(f"  - 轮询间隔: {api.polling_interval}s")
        print(f"  - 最大轮询时间: {api.max_polling_time}s")
        
        # 测试创建任务
        task = await api.create_generation_task(
            task_id="test_001",
            prompt="test prompt",
            model_type=ModelType.ANIME
        )
        
        print(f"✅ 测试任务创建: {task.status}")
        if task.mark_id:
            print(f"  - Mark ID: {task.mark_id}")
        else:
            print(f"  - 错误: {task.error_message}")
        
        return True
        
    except Exception as e:
        print(f"❌ 远程API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("="*60)
    print("🧪 真实图像生成API测试")
    print("="*60)
    
    # 基本连接测试
    basic_test = await test_remote_api_basic()
    
    if basic_test:
        # 完整图像生成测试
        full_test = await test_real_image_generation()
        
        if full_test:
            print("\n🎉 所有测试通过！图像生成API配置正确。")
        else:
            print("\n⚠️ 图像生成测试失败，但基本连接正常。")
    else:
        print("\n❌ 基本连接测试失败，请检查网络和API配置。")
    
    print("="*60)

if __name__ == "__main__":
    asyncio.run(main())