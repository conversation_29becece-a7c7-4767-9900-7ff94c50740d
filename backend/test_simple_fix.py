#!/usr/bin/env python3
"""测试简化修复后的效果"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from agent.llm_utils import clean_json_response
import json


def test_basic_json_cleaning():
    """测试基础JSON清理功能"""
    print("🧪 测试基础JSON清理...")
    
    # 测试正常的JSON
    normal_json = '''
{
  "processed_story": "小猫咪在花园里追逐蝴蝶",
  "processed_story_en": "A kitten chasing butterflies in the garden",
  "story_type": "expansion",
  "rationale": "扩写简短故事"
}
'''
    
    print("测试正常JSON:")
    print(normal_json)
    
    try:
        cleaned = clean_json_response(normal_json)
        print(f"\n清理后: {cleaned}")
        
        parsed = json.loads(cleaned)
        print(f"\n✅ 解析成功: {list(parsed.keys())}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return False


def test_markdown_wrapped_json():
    """测试包含markdown包装的JSON"""
    print("\n🧪 测试markdown包装的JSON...")
    
    # 测试包含markdown包装的JSON
    markdown_json = '''```json
{
  "processed_story": "小猫咪在花园里追逐蝴蝶",
  "processed_story_en": "A kitten chasing butterflies in the garden",
  "story_type": "expansion",
  "rationale": "扩写简短故事"
}
```'''
    
    print("测试markdown包装的JSON:")
    print(markdown_json)
    
    try:
        cleaned = clean_json_response(markdown_json)
        print(f"\n清理后: {cleaned}")
        
        parsed = json.loads(cleaned)
        print(f"\n✅ 解析成功: {list(parsed.keys())}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return False


def test_json_with_extra_text():
    """测试包含额外文本的JSON"""
    print("\n🧪 测试包含额外文本的JSON...")
    
    # 测试包含额外文本的JSON
    extra_text_json = '''这是一个JSON响应：

{
  "processed_story": "小猫咪在花园里追逐蝴蝶",
  "processed_story_en": "A kitten chasing butterflies in the garden",
  "story_type": "expansion",
  "rationale": "扩写简短故事"
}

以上是处理结果。'''
    
    print("测试包含额外文本的JSON:")
    print(extra_text_json)
    
    try:
        cleaned = clean_json_response(extra_text_json)
        print(f"\n清理后: {cleaned}")
        
        parsed = json.loads(cleaned)
        print(f"\n✅ 解析成功: {list(parsed.keys())}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试简化修复后的效果...\n")
    
    test1 = test_basic_json_cleaning()
    test2 = test_markdown_wrapped_json()
    test3 = test_json_with_extra_text()
    
    print("\n📊 测试总结:")
    print(f"   基础JSON清理: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"   Markdown包装: {'✅ 通过' if test2 else '❌ 失败'}")
    print(f"   额外文本处理: {'✅ 通过' if test3 else '❌ 失败'}")
    
    if test1 and test2 and test3:
        print("\n🎉 所有基础测试都通过！")
        print("现在可以尝试重新运行插画生成功能。")
    else:
        print("\n⚠️  部分测试失败，需要进一步调整")


if __name__ == "__main__":
    main()
