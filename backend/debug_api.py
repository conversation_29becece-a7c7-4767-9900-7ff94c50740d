#!/usr/bin/env python3
"""
调试远程图像生成API
"""

import asyncio
import httpx
import json

async def test_direct_api_call():
    """直接测试API调用"""
    print("🔍 直接测试API调用...")
    
    # 使用文档中的确切格式
    url = "http://192.168.5.200/api/gen/create"
    headers = {
        "Content-Type": "application/json",
        "authorization": "ca5f728c93fef939e6f23baa34b53ed9aa2768dc",
        "Platform": "Web"
    }
    
    # 动漫模型的确切请求格式
    payload = {
        "highPixels": False,
        "model_id": "cb4af9c7-41b0-47d3-944a-221446c7b8bc",
        "prompt": "lady",
        "negative_prompt": "(nsfw:0.7), (worst quality:1.5), (low quality:1.5), (normal quality:1.5), lowres,watermark, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, jpeg artifacts, signature, username, blurry, artist name",
        "resolution": {
            "width": 768,
            "height": 1344,
            "batch_size": 1
        },
        "model_ability": {
            "anime_style_control": None
        },
        "seed": 82879612712,
        "steps": 25,
        "cfg": 7,
        "sampler_name": "euler_ancestral",
        "scheduler": "karras",
        "ponyTags": {},
        "denoise": 1,
        "hires_fix_denoise": 0.5,
        "hires_scale": 2,
        "multi_img2img_info": {"style_list": []},
        "img_control_info": {"style_list": []},
        "continueCreate": False
    }
    
    print("📤 请求URL:", url)
    print("📤 请求头:", headers)
    print("📤 请求载荷:", json.dumps(payload, indent=2, ensure_ascii=False))
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(url, json=payload, headers=headers)
            
            print(f"📥 响应状态码: {response.status_code}")
            print(f"📥 响应头: {dict(response.headers)}")
            print(f"📥 响应文本: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 成功解析JSON: {json.dumps(result, indent=2, ensure_ascii=False)}")
                return result
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                return None
                
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_art_model():
    """测试艺术模型"""
    print("\n🎨 测试艺术模型...")
    
    url = "http://192.168.5.200/api/gen/create"
    headers = {
        "Content-Type": "application/json",
        "authorization": "ca5f728c93fef939e6f23baa34b53ed9aa2768dc",
        "Platform": "Web"
    }
    
    # 艺术模型的确切请求格式
    payload = {
        "highPixels": False,
        "model_id": "23887bba-507e-4249-a0e3-6951e4027f2b",
        "prompt": "a girl",
        "negative_prompt": "",
        "resolution": {
            "width": 768,
            "height": 1344,
            "batch_size": 1
        },
        "model_ability": {},
        "seed": 98175180350,
        "steps": 6,
        "cfg": 1,
        "sampler_name": "euler",
        "scheduler": "normal",
        "ponyTags": {},
        "denoise": 1,
        "hires_fix_denoise": 0.5,
        "hires_scale": 2,
        "multi_img2img_info": {"style_list": []},
        "img_control_info": {"style_list": []},
        "continueCreate": False
    }
    
    print("📤 艺术模型请求载荷:", json.dumps(payload, indent=2, ensure_ascii=False))
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(url, json=payload, headers=headers)
            
            print(f"📥 艺术模型响应状态码: {response.status_code}")
            print(f"📥 艺术模型响应文本: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 艺术模型成功: {json.dumps(result, indent=2, ensure_ascii=False)}")
                return result
            else:
                print(f"❌ 艺术模型HTTP错误: {response.status_code}")
                return None
                
    except Exception as e:
        print(f"❌ 艺术模型请求异常: {e}")
        return None

async def main():
    """主测试函数"""
    print("="*60)
    print("🔧 API调试工具")
    print("="*60)
    
    # 测试动漫模型
    result1 = await test_direct_api_call()
    
    # 测试艺术模型
    result2 = await test_art_model()
    
    print("\n" + "="*60)
    print("📊 测试总结")
    print("="*60)
    print(f"动漫模型测试: {'✅ 成功' if result1 else '❌ 失败'}")
    print(f"艺术模型测试: {'✅ 成功' if result2 else '❌ 失败'}")

if __name__ == "__main__":
    asyncio.run(main())