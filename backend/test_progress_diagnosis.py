#!/usr/bin/env python3
"""
简化的进度推送测试脚本

使用模拟方式逐步执行，确保进度推送机制正常工作
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime


async def test_simple_progress():
    """测试简单的进度推送"""
    print("🧪 测试简化的进度推送机制")
    
    # 创建一个简单的测试请求
    test_data = {
        "user_input": "简单测试故事",
        "style_preference": "anime", 
        "num_panels": 2  # 减少分镜数量，缩短测试时间
    }
    
    async with aiohttp.ClientSession() as session:
        print(f"📤 发送简化测试请求: {datetime.now().strftime('%H:%M:%S')}")
        
        try:
            # 步骤1: 发送开始请求
            async with session.post(
                "http://localhost:8080/api/start-generation",
                json=test_data,
                headers={"Platform": "Web"}
            ) as response:
                print(f"📬 开始请求响应状态: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    session_id = result.get("session_id")
                    print(f"✅ 获得会话ID: {session_id}")
                    
                    if not session_id:
                        print("❌ 未获得会话ID，无法继续测试")
                        return
                    
                    # 步骤2: 立即开始监听进度流
                    print(f"📡 开始监听进度流...")
                    await monitor_progress_stream(session, session_id)
                    
                else:
                    text = await response.text()
                    print(f"❌ 开始请求失败: {response.status} - {text}")
                    
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")


async def monitor_progress_stream(session, session_id):
    """监听进度流"""
    try:
        print(f"🔗 连接进度流: /api/progress/{session_id}")
        
        async with session.get(
            f"http://localhost:8080/api/progress/{session_id}",
            headers={"Platform": "Web"}
        ) as response:
            print(f"📊 进度流响应状态: {response.status}")
            print(f"📋 响应头: {dict(response.headers)}")
            
            if response.status != 200:
                text = await response.text()
                print(f"❌ 进度流连接失败: {text}")
                return
            
            # 检查媒体类型
            content_type = response.headers.get('content-type', '')
            print(f"📝 内容类型: {content_type}")
            
            if 'text/event-stream' not in content_type:
                print(f"⚠️  警告: 媒体类型不是text/event-stream")
            
            progress_count = 0
            last_update_time = time.time()
            
            print(f"⏳ 开始接收进度数据...")
            
            async for line in response.content:
                current_time = time.time()
                line_str = line.decode('utf-8').rstrip()
                
                if line_str.startswith('data: '):
                    progress_count += 1
                    data_str = line_str[6:]  # 去掉 'data: ' 前缀
                    
                    try:
                        data = json.loads(data_str)
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        elapsed = current_time - last_update_time
                        
                        print(f"[{timestamp}] #{progress_count} (+{elapsed:.2f}s): {data}")
                        last_update_time = current_time
                        
                        # 检查特殊事件
                        if data.get("type") == "result":
                            print(f"🏁 收到最终结果")
                            break
                        elif data.get("type") == "error":
                            print(f"❌ 收到错误信息")
                            break
                            
                    except json.JSONDecodeError as e:
                        print(f"⚠️  JSON解析错误: {e} - 数据: {data_str}")
                
                elif line_str.strip():  # 非空行但不是数据行
                    print(f"📝 其他数据: {line_str}")
                
                # 超时保护
                if current_time - last_update_time > 60:  # 1分钟无更新
                    print(f"⏰ 超时：1分钟无更新，停止监听")
                    break
            
            print(f"\n📊 监听总结:")
            print(f"   - 接收到的进度更新: {progress_count}")
            print(f"   - 监听时长: {time.time() - (last_update_time - (current_time - last_update_time)):.2f}秒")
            
    except Exception as e:
        print(f"❌ 进度流监听异常: {str(e)}")
        import traceback
        traceback.print_exc()


async def test_server_health():
    """测试服务器健康状态"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8080/api/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 服务器健康: {data}")
                    return True
                else:
                    print(f"❌ 服务器状态异常: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ 无法连接服务器: {str(e)}")
        return False


async def main():
    print("=" * 50)
    print("🔧 进度推送机制诊断测试")
    print("=" * 50)
    
    # 检查服务器
    if not await test_server_health():
        print("\n请确保服务器运行在 http://localhost:8080")
        return
    
    print()
    # 执行测试
    await test_simple_progress()
    
    print("\n" + "=" * 50)
    print("🏁 诊断测试完成")
    print("=" * 50)


if __name__ == "__main__":
    asyncio.run(main())