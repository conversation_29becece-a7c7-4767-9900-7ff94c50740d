#!/usr/bin/env python3
"""
测试标准化API格式
"""

import requests
import json

def test_standard_api_format():
    """测试标准化API格式"""
    print("🧪 测试标准化API格式")
    print("="*50)
    
    url = "http://localhost:8080/api/generate-illustration"
    
    # 测试数据
    test_data = {
        "user_input": "小猫咪在花园里玩耍",
        "style_preference": "anime", 
        "num_panels": 2
    }
    
    print(f"📤 发送请求到: {url}")
    print(f"📤 请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data, timeout=120)
        
        print(f"\n📥 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"\n📋 响应结构分析:")
            print(f"✅ 顶级字段:")
            print(f"  - success: {result.get('success')} ({type(result.get('success')).__name__})")
            print(f"  - message: {result.get('message', 'N/A')[:100]}...")
            print(f"  - data: {'存在' if result.get('data') else '缺失'}")
            print(f"  - error: {result.get('error', 'N/A')}")
            
            # 分析data字段
            data = result.get('data', {})
            if data:
                print(f"\n📊 data字段内容:")
                print(f"  - processed_story: {'存在' if data.get('processed_story') else '缺失'}")
                print(f"  - characters: {len(data.get('characters', []))} 个")
                print(f"  - storyboards: {len(data.get('storyboards', []))} 个")  
                print(f"  - generated_images: {len(data.get('generated_images', []))} 个")
                print(f"  - final_illustration: {'存在' if data.get('final_illustration') else '缺失'}")
                
                # 显示详细信息
                if data.get('characters'):
                    print(f"\n👥 角色信息:")
                    for i, char in enumerate(data['characters'][:3]):
                        print(f"  {i+1}. {char.get('name', 'N/A')}: {char.get('description', 'N/A')[:50]}...")
                
                if data.get('storyboards'):
                    print(f"\n🎬 分镜信息:")
                    for i, board in enumerate(data['storyboards'][:3]):
                        print(f"  {i+1}. Panel {board.get('panel_id', 'N/A')}: {board.get('scene_description', 'N/A')[:50]}...")
                
                if data.get('generated_images'):
                    print(f"\n🖼️ 图片信息:")
                    for i, img in enumerate(data['generated_images'][:3]):
                        status = "✅成功" if img.get('success') else "❌失败"
                        print(f"  {i+1}. Panel {img.get('panel_id', 'N/A')}: {status}")
                        if img.get('image_url'):
                            print(f"      URL: {img['image_url'][:60]}...")
            else:
                print(f"\n⚠️ data字段为空或不存在")
            
            print(f"\n🎯 API格式验证结果:")
            
            # 验证标准化格式
            format_valid = True
            issues = []
            
            if not isinstance(result.get('success'), bool):
                issues.append("success字段应为boolean类型")
                format_valid = False
                
            if not result.get('message'):
                issues.append("message字段不能为空")
                format_valid = False
                
            if result.get('success') and not result.get('data'):
                issues.append("成功时data字段不能为空")
                format_valid = False
            
            if format_valid:
                print("✅ API格式完全符合标准化要求")
            else:
                print("❌ API格式存在问题:")
                for issue in issues:
                    print(f"  - {issue}")
            
            return result
            
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 请确保后端服务器正在运行 (http://localhost:8080)")
        return None
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

if __name__ == "__main__":
    test_standard_api_format()