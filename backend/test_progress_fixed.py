#!/usr/bin/env python3
"""
测试修复后的进度回调
"""
import asyncio
import sys
import os
import time

# 添加src目录到路径
sys.path.append('src')

async def test_progress_fixed():
    """测试修复后的进度回调"""
    try:
        from agent.illustration_graph import generate_illustration
        
        print('🔄 测试修复后的进度回调...')
        
        # 收集进度更新
        progress_updates = []
        
        def progress_callback(node_name, status, data=None):
            timestamp = time.time()
            update = f'{node_name} -> {status}'
            print(f'📊 进度: {update}')
            progress_updates.append((node_name, status, timestamp))
        
        # 执行工作流（使用较短的故事避免超时）
        start_time = time.time()
        
        # 设置较短的超时时间，如果图像生成太慢就停止
        try:
            result = await asyncio.wait_for(
                generate_illustration(
                    user_input='小猫玩耍',
                    style_preference='anime',
                    num_panels=1,  # 只生成1个分镜减少时间
                    progress_callback=progress_callback
                ),
                timeout=60  # 60秒超时
            )
        except asyncio.TimeoutError:
            print('⏰ 工作流超时，但我们已经收集了进度信息')
            result = None
        
        end_time = time.time()
        
        print(f'\n📈 总共收到 {len(progress_updates)} 个进度更新')
        print(f'⏱️ 耗时: {end_time - start_time:.2f}秒')
        
        # 显示所有进度更新
        print('\n📋 详细进度列表:')
        for i, (node, status, timestamp) in enumerate(progress_updates):
            relative_time = timestamp - start_time
            print(f'  {i+1:2d}. [{relative_time:6.2f}s] {node} -> {status}')
        
        # 分析节点覆盖情况
        expected_nodes = [
            'input_handler',
            'story_splitter', 
            'character_extractor',
            'storyboard_generator',
            'scene_prompt_optimizer',
            'unified_image_generator',
            'image_merger',
            'finalize_illustration'
        ]
        
        print(f'\n🔍 节点执行分析:')
        executed_nodes = set(node for node, _, _ in progress_updates)
        for node in expected_nodes:
            status = '✅' if node in executed_nodes else '❌'
            print(f'  {status} {node}')
        
        # 检查进度回调是否正常工作
        if len(progress_updates) >= 4:  # 至少前两个节点的开始和完成
            print('\n✅ 进度回调修复成功！')
        else:
            print('\n❌ 进度回调仍有问题')
            
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_progress_fixed())
