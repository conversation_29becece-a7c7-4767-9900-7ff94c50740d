#!/usr/bin/env python3
"""
简单的服务器启动脚本用于测试
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
backend_dir = Path(__file__).parent
src_dir = backend_dir / "src"
sys.path.insert(0, str(src_dir))

# 设置环境变量
os.environ.setdefault("HOST", "0.0.0.0")
os.environ.setdefault("PORT", "8080")

if __name__ == "__main__":
    import uvicorn
    from agent.app import app
    
    print("🚀 启动测试服务器")
    print(f"📍 地址: http://localhost:8080")
    print("=" * 50)
    
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8080,
            reload=False,
            log_level="info"
        )
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)