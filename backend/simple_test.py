#!/usr/bin/env python3
"""
简化的测试脚本，用于验证基本功能
"""

import os
import sys
from pathlib import Path

# 添加src目录到Python路径
backend_dir = Path(__file__).parent
src_dir = backend_dir / "src"
sys.path.insert(0, str(src_dir))

def test_imports():
    """测试基本导入"""
    print("🔍 测试基本导入...")
    
    try:
        from agent.configuration import Configuration
        print("✅ Configuration导入成功")
    except Exception as e:
        print(f"❌ Configuration导入失败: {e}")
        return False
    
    try:
        from agent.llm_utils import get_default_llm
        print("✅ LLM工具导入成功")
    except Exception as e:
        print(f"❌ LLM工具导入失败: {e}")
        return False
    
    try:
        from agent.state import IllustrationState
        print("✅ 状态定义导入成功")
    except Exception as e:
        print(f"❌ 状态定义导入失败: {e}")
        return False
    
    try:
        from agent.tools_and_schemas import StoryProcessingResult
        print("✅ 工具和模式导入成功")
    except Exception as e:
        print(f"❌ 工具和模式导入失败: {e}")
        return False
    
    return True

def test_environment():
    """测试环境配置"""
    print("\n🔧 测试环境配置...")
    
    # 检查.env文件
    env_file = backend_dir / ".env"
    if env_file.exists():
        print("✅ .env文件存在")
    else:
        print("⚠️ .env文件不存在，从.env.example创建...")
        env_example = backend_dir / ".env.example"
        if env_example.exists():
            import shutil
            shutil.copy(env_example, env_file)
            print("✅ 已创建.env文件")
        else:
            print("❌ .env.example文件不存在")
            return False
    
    # 加载环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv(env_file)
        print("✅ 环境变量加载成功")
    except Exception as e:
        print(f"❌ 环境变量加载失败: {e}")
        return False
    
    # 检查关键环境变量
    qwen_key = os.getenv("QWEN_API_KEY") or os.getenv("DASHSCOPE_API_KEY")
    if qwen_key and qwen_key != "your-qwen-api-key-here":
        print("✅ Qwen API密钥已配置")
    else:
        print("⚠️ Qwen API密钥未配置或使用默认值")
    
    return True

def test_llm_initialization():
    """测试LLM初始化"""
    print("\n🤖 测试LLM初始化...")
    
    try:
        from agent.llm_utils import get_default_llm
        
        # 尝试初始化LLM（不实际调用）
        llm = get_default_llm(temperature=0.1)
        print("✅ LLM初始化成功")
        return True
    except Exception as e:
        print(f"❌ LLM初始化失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n⚙️ 测试基本功能...")
    
    try:
        from agent.illustration_nodes import input_handler
        from agent.state import IllustrationState
        from langchain_core.runnables import RunnableConfig
        
        # 创建测试状态
        test_state = {
            "user_input": "测试故事",
            "style_preference": "anime",
            "num_panels": 4,
            "messages": []
        }
        
        # 创建配置
        config = RunnableConfig(configurable={})
        
        print("✅ 基本功能组件加载成功")
        print("⚠️ 跳过实际LLM调用测试（需要有效API密钥）")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def test_api_server():
    """测试API服务器启动"""
    print("\n🌐 测试API服务器...")
    
    try:
        from agent.app import app
        print("✅ FastAPI应用加载成功")
        
        # 检查路由
        routes = [route.path for route in app.routes]
        expected_routes = ["/api/generate-illustration", "/api/health", "/api/styles"]
        
        for route in expected_routes:
            if any(route in r for r in routes):
                print(f"✅ 路由 {route} 存在")
            else:
                print(f"⚠️ 路由 {route} 可能不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ API服务器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 插画生成Agent - 简化测试")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_imports),
        ("环境配置测试", test_environment),
        ("LLM初始化测试", test_llm_initialization),
        ("基本功能测试", test_basic_functionality),
        ("API服务器测试", test_api_server),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以启动。")
        print("\n🚀 下一步:")
        print("1. 启动后端: python run_server.py")
        print("2. 启动前端: cd ../frontend && npm run dev")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置。")
        print("\n🔧 建议:")
        print("1. 检查.env文件中的API密钥配置")
        print("2. 确保所有依赖已安装: pip install -e .")
        print("3. 检查Python路径和导入")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
