#!/usr/bin/env python3
"""
简化的进度测试
"""
import sys
import os

# 添加src目录到路径
sys.path.append('src')

def test_progress_callback():
    """测试进度回调是否正常工作"""
    print('🔄 测试进度回调机制...')
    
    # 模拟状态
    test_state = {
        "user_input": "测试输入",
        "progress_callback": None  # 先设为None测试
    }
    
    # 测试1: 没有回调函数
    print('\n📋 测试1: 没有回调函数')
    progress_callback = test_state.get("progress_callback")
    print(f'  progress_callback: {progress_callback}')
    if progress_callback:
        progress_callback("test_node", "in_progress")
        print('  ✅ 回调函数被调用')
    else:
        print('  ❌ 回调函数为None，未调用')
    
    # 测试2: 有回调函数
    print('\n📋 测试2: 有回调函数')
    def my_callback(node_name, status, data=None):
        print(f'  📊 回调被调用: {node_name} -> {status}')
    
    test_state["progress_callback"] = my_callback
    progress_callback = test_state.get("progress_callback")
    print(f'  progress_callback: {progress_callback}')
    if progress_callback:
        progress_callback("test_node", "in_progress")
        print('  ✅ 回调函数被调用')
    else:
        print('  ❌ 回调函数为None，未调用')

def test_node_execution():
    """测试单个节点的执行"""
    print('\n🔄 测试单个节点执行...')
    
    try:
        from agent.illustration_nodes import input_handler
        from agent.configuration import Configuration
        from langchain_core.runnables import RunnableConfig
        
        # 创建回调函数
        def my_callback(node_name, status, data=None):
            print(f'📊 节点进度: {node_name} -> {status}')
            if data:
                print(f'   数据: {data}')
        
        # 创建测试状态
        test_state = {
            "user_input": "小猫在花园里玩耍",
            "style_preference": "anime",
            "num_panels": 2,
            "messages": [],
            "progress_callback": my_callback
        }
        
        # 创建配置
        config = RunnableConfig()
        
        print('📍 调用input_handler节点...')
        result = input_handler(test_state, config)
        
        print(f'✅ 节点执行完成')
        print(f'📊 返回结果键: {list(result.keys()) if isinstance(result, dict) else "Not dict"}')
        
    except Exception as e:
        print(f'❌ 节点测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_progress_callback()
    test_node_execution()
