#!/usr/bin/env python3
"""
测试插画生成API的返回格式
"""

import requests
import json

def test_api_format():
    """测试API返回格式"""
    print("🧪 测试插画生成API返回格式")
    
    url = "http://localhost:8080/api/generate-illustration"
    
    # 测试数据
    test_data = {
        "user_input": "小猫咪在花园里玩耍",
        "style_preference": "anime",
        "num_panels": 2
    }
    
    print(f"📤 发送请求: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data, timeout=60)
        
        print(f"📥 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"📄 响应格式检查:")
            print(f"  - success字段: {result.get('success', 'MISSING')}")
            print(f"  - message字段: {result.get('message', 'MISSING')}")
            print(f"  - processed_story字段: {'存在' if result.get('processed_story') else '缺失'}")
            print(f"  - characters字段: {'存在' if result.get('characters') else '缺失'} (数量: {len(result.get('characters', []))})")
            print(f"  - storyboards字段: {'存在' if result.get('storyboards') else '缺失'} (数量: {len(result.get('storyboards', []))})")
            print(f"  - generated_images字段: {'存在' if result.get('generated_images') else '缺失'} (数量: {len(result.get('generated_images', []))})")
            print(f"  - final_illustration字段: {'存在' if result.get('final_illustration') else '缺失'}")
            
            # 显示部分数据内容
            if result.get('characters'):
                print(f"\n👥 角色信息示例:")
                for i, char in enumerate(result['characters'][:2]):  # 只显示前2个
                    print(f"  角色{i+1}: {char.get('name', 'N/A')} - {char.get('description', 'N/A')}")
            
            if result.get('storyboards'):
                print(f"\n🎬 分镜信息示例:")
                for i, board in enumerate(result['storyboards'][:2]):  # 只显示前2个
                    print(f"  分镜{i+1}: {board.get('scene_description', 'N/A')}")
            
            if result.get('generated_images'):
                print(f"\n🖼️ 图片信息示例:")
                for i, img in enumerate(result['generated_images'][:2]):  # 只显示前2个
                    status = "✅" if img.get('success') else "❌"
                    print(f"  图片{i+1}: {status} Panel {img.get('panel_id', 'N/A')}")
            
            print(f"\n✅ API返回格式测试完成!")
            return result
            
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

if __name__ == "__main__":
    test_api_format()