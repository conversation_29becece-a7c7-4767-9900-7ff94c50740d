#!/usr/bin/env python3
"""
测试多个节点的进度回调
"""
import sys
import os

# 添加src目录到路径
sys.path.append('src')

def test_multi_node_progress():
    """测试多个节点的进度回调传递"""
    print('🔄 测试多个节点的进度回调传递...')
    
    try:
        from agent.illustration_nodes import input_handler, story_splitter
        from agent.configuration import Configuration
        from langchain_core.runnables import RunnableConfig
        
        # 创建回调函数
        progress_updates = []
        def my_callback(node_name, status, data=None):
            update = f'{node_name} -> {status}'
            print(f'📊 节点进度: {update}')
            progress_updates.append((node_name, status))
        
        # 创建初始状态
        initial_state = {
            "user_input": "小猫在花园里玩耍",
            "style_preference": "anime",
            "num_panels": 2,
            "messages": [],
            "progress_callback": my_callback
        }
        
        # 创建配置
        config = RunnableConfig()
        
        print('📍 第1步: 调用input_handler节点...')
        result1 = input_handler(initial_state, config)
        print(f'✅ input_handler完成，返回键: {list(result1.keys())}')
        print(f'🔍 progress_callback是否保留: {"progress_callback" in result1}')
        
        # 更新状态
        updated_state = {**initial_state, **result1}
        
        print('\n📍 第2步: 调用story_splitter节点...')
        result2 = story_splitter(updated_state, config)
        print(f'✅ story_splitter完成，返回键: {list(result2.keys())}')
        print(f'🔍 progress_callback是否保留: {"progress_callback" in result2}')
        
        print(f'\n📈 总共收到 {len(progress_updates)} 个进度更新:')
        for i, (node, status) in enumerate(progress_updates):
            print(f'  {i+1}. {node} -> {status}')
            
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_multi_node_progress()
