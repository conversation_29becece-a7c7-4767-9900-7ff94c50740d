"""启动插画生成Agent服务器"""

import os
import sys
import uvicorn
from pathlib import Path
from dotenv import load_dotenv

# 添加src目录到Python路径
backend_dir = Path(__file__).parent
src_dir = backend_dir / "src"
sys.path.insert(0, str(src_dir))

# 加载环境变量
load_dotenv()

if __name__ == "__main__":
    # 获取配置
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8080))
    debug = os.getenv("DEBUG", "false").lower() == "true"

    print("🚀 启动插画生成Agent服务器")
    print(f"📍 地址: http://{host}:{port}")
    print(f"🔧 调试模式: {debug}")
    print(f"🗂️ 源码目录: {src_dir}")
    print("=" * 50)

    # 测试导入
    try:
        from agent.app import app
        print("✅ 应用导入成功")
    except Exception as e:
        print(f"❌ 应用导入失败: {e}")
        sys.exit(1)

    # 启动服务器
    if debug:
        # 开发模式：使用字符串导入以支持reload
        uvicorn.run(
            "agent.app:app",
            host=host,
            port=port,
            reload=True,
            log_level="debug"
        )
    else:
        # 生产模式：直接传递app对象
        uvicorn.run(
            app,
            host=host,
            port=port,
            reload=False,
            log_level="info"
        )
