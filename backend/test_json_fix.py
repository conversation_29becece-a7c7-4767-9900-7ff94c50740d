#!/usr/bin/env python3
"""测试JSON修复功能"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from agent.llm_utils import fix_truncated_json, clean_json_response
import json


def test_truncated_json():
    """测试截断JSON修复"""
    print("🧪 测试截断JSON修复...")
    
    # 模拟截断的JSON
    truncated_json = '''
{
  "processed_story": "在一个阳光明媚的午后，小猫咪Mimi在繁花似锦的花园里嬉戏玩耍。她灵动的眼眸被一只翩翩起舞的蓝斑蝶所吸引，瞬间激发了她无尽的好奇心与追逐本能。Mimi轻盈地跳跃，尾巴高高翘起，宛如一支蓄势待发的箭矢。蝴蝶忽上忽下，时左时右，引领着Mimi穿梭于五彩斑斓的花丛之间，留下一串串欢快的足迹。在追逐的高潮中，蝴蝶突然消失在一个隐蔽于藤蔓与玫瑰交织处的神秘洞穴口。Mimi停下了脚步，凝视着那幽深而神秘的入口，心中充满了探求未知的渴望。她鼓足勇气，迈着谨慎的步伐靠近洞口，洞内传来微弱的荧光与阵阵清风，似乎在召唤她深入探索。Mimi踏入洞穴，只见石壁上镶嵌着发光的矿石，映照出奇幻的光影。沿着蜿蜒曲折的通道，她偶遇了一群友善的小老鼠，他们指引Mimi找到了一处隐藏的瀑布，瀑布下方是一潭清澈见底的碧水，水中倒映着彩虹般的光芒。Mimi欣喜地跳入水中嬉戏，享受这意外的冒险收获。在洞穴深处，Mimi终于发现了那只蓝斑蝶，它正停在一块巨大的水晶之上，仿佛在守护某种秘密。Mimi静静靠近，与蝴蝶共享这片神秘的宁静。随后，她带着满足与新奇的记...
  "story_type": "expansion",
'''
    
    print("原始截断JSON:")
    print(truncated_json)
    print("\n" + "="*50 + "\n")
    
    try:
        # 测试修复函数
        fixed_json = fix_truncated_json(truncated_json)
        print("修复后的JSON:")
        print(fixed_json)
        print("\n" + "="*50 + "\n")
        
        # 尝试解析
        parsed = json.loads(fixed_json)
        print("✅ JSON解析成功！")
        print(f"字段: {list(parsed.keys())}")
        
        for key, value in parsed.items():
            if isinstance(value, str) and len(value) > 50:
                print(f"{key}: {value[:50]}...")
            else:
                print(f"{key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False


def test_clean_json_response():
    """测试完整的JSON清理流程"""
    print("\n🧪 测试完整的JSON清理流程...")
    
    # 模拟LLM输出（包含markdown包装和截断）
    llm_output = '''```json
{
  "processed_story": "小猫咪在花园里追逐蝴蝶的故事...",
  "processed_story_en": "A story about a kitten chasing butterflies in the garden...",
  "story_type": "expansion",
  "rationale": "扩写简短故事以增加细节...
}
```'''
    
    print("模拟LLM输出:")
    print(llm_output)
    print("\n" + "="*50 + "\n")
    
    try:
        cleaned = clean_json_response(llm_output)
        print("清理后的JSON:")
        print(cleaned)
        print("\n" + "="*50 + "\n")
        
        parsed = json.loads(cleaned)
        print("✅ JSON解析成功！")
        print(f"字段: {list(parsed.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试JSON修复功能...\n")
    
    test1_passed = test_truncated_json()
    test2_passed = test_clean_json_response()
    
    print("\n📊 测试总结:")
    print(f"   截断JSON修复: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"   完整清理流程: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试都通过！")
    else:
        print("\n⚠️  部分测试失败，需要进一步调整")


if __name__ == "__main__":
    main()
