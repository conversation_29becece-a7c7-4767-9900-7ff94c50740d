#!/usr/bin/env python3
"""调试JSON解析问题"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from agent.llm_utils import clean_json_response, create_structured_output_chain
from agent.tools_and_schemas import StoryProcessingResult
from agent.llm_utils import get_default_llm
import json


def test_simple_json():
    """测试简单的JSON解析"""
    print("🧪 测试简单JSON解析...")
    
    # 测试一个简单的JSON
    simple_json = '''
{
  "processed_story": "小猫咪在花园里追逐蝴蝶",
  "processed_story_en": "A kitten chasing butterflies in the garden",
  "story_type": "expansion",
  "rationale": "扩写简短故事"
}
'''
    
    print("测试JSON:")
    print(simple_json)
    
    try:
        cleaned = clean_json_response(simple_json)
        print(f"\n清理后: {cleaned}")
        
        parsed = json.loads(cleaned)
        print(f"\n解析成功: {parsed}")
        
        # 测试Pydantic验证
        result = StoryProcessingResult.model_validate(parsed)
        print(f"\nPydantic验证成功: {result}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        print(f"错误类型: {type(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_llm_chain():
    """测试LLM链"""
    print("\n🧪 测试LLM链...")
    
    try:
        # 创建LLM
        llm = get_default_llm("qwen-max-0403", temperature=0.7)
        
        # 创建结构化输出链
        structured_llm = create_structured_output_chain(llm, StoryProcessingResult)
        
        # 测试简单的提示
        test_prompt = """请返回JSON格式的结果：
{
  "processed_story": "测试故事",
  "processed_story_en": "Test story",
  "story_type": "test",
  "rationale": "测试"
}"""
        
        print(f"测试提示: {test_prompt}")
        
        result = structured_llm.invoke(test_prompt)
        print(f"\n✅ LLM链测试成功: {result}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ LLM链测试失败: {e}")
        print(f"错误类型: {type(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_error_message():
    """测试错误信息处理"""
    print("\n🧪 测试错误信息处理...")
    
    # 模拟一个JSON解析错误
    try:
        json.loads('{\n  "processed_story"')
    except json.JSONDecodeError as e:
        print(f"原始错误: {e}")
        print(f"错误字符串: {str(e)}")
        print(f"错误字符串表示: {repr(str(e))}")
        
        # 检查错误信息是否包含特定内容
        error_str = str(e)
        if "processed_story" in error_str:
            print("✅ 错误信息包含'processed_story'")
        else:
            print("❌ 错误信息不包含'processed_story'")


def main():
    """主测试函数"""
    print("🚀 开始调试JSON解析问题...\n")
    
    test1 = test_simple_json()
    test2 = test_llm_chain()
    test_error_message()
    
    print("\n📊 测试总结:")
    print(f"   简单JSON解析: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"   LLM链测试: {'✅ 通过' if test2 else '❌ 失败'}")


if __name__ == "__main__":
    main()
