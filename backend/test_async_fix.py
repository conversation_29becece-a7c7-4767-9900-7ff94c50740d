#!/usr/bin/env python3
"""
测试异步调用修复的脚本

测试 /api/start-generation 接口是否能立即返回，
以及进度推送是否正常工作。
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime


async def test_async_generation():
    """测试异步生成功能"""
    print("🚀 开始测试异步插画生成功能")
    
    # 测试数据
    test_data = {
        "user_input": "一个勇敢的小女孩在森林中遇到了一只友善的小鹿，它们一起踏上了寻找失落的魔法水晶的冒险之旅。",
        "style_preference": "anime", 
        "num_panels": 4
    }
    
    async with aiohttp.ClientSession() as session:
        print(f"📤 发送生成请求: {datetime.now().strftime('%H:%M:%S.%f')[:-3]}")
        
        # 测试 /api/start-generation 接口的响应时间
        start_time = time.time()
        
        try:
            async with session.post(
                "http://localhost:8080/api/start-generation",
                json=test_data,
                headers={"Platform": "Web"}
            ) as response:
                response_time = time.time() - start_time
                print(f"📬 接收到响应: {datetime.now().strftime('%H:%M:%S.%f')[:-3]}")
                print(f"⏱️  响应时间: {response_time:.3f}秒")
                
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 请求成功: {result}")
                    
                    session_id = result.get("session_id")
                    if session_id:
                        print(f"🆔 获得会话ID: {session_id}")
                        
                        # 验证响应时间是否足够快（小于2秒）
                        if response_time < 2.0:
                            print(f"✅ 响应时间验证通过: {response_time:.3f}秒 < 2.0秒")
                        else:
                            print(f"❌ 响应时间过慢: {response_time:.3f}秒 >= 2.0秒")
                            
                        # 测试进度流
                        await test_progress_stream(session, session_id)
                    else:
                        print("❌ 未获得会话ID")
                else:
                    print(f"❌ 请求失败: {response.status}")
                    text = await response.text()
                    print(f"错误信息: {text}")
                    
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")


async def test_progress_stream(session, session_id):
    """测试进度流功能"""
    print(f"\n📡 开始监听进度流: {session_id}")
    
    try:
        async with session.get(
            f"http://localhost:8080/api/progress/{session_id}",
            headers={"Platform": "Web"}
        ) as response:
            print(f"🔗 进度流连接状态: {response.status}")
            
            if response.status == 200:
                print(f"📊 开始接收进度更新...")
                
                progress_count = 0
                start_time = time.time()
                
                async for line in response.content:
                    if line.startswith(b'data: '):
                        progress_count += 1
                        data_str = line[6:].decode('utf-8').strip()
                        
                        try:
                            data = json.loads(data_str)
                            current_time = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                            print(f"[{current_time}] 进度更新 #{progress_count}: {data}")
                            
                            # 检查是否为最终结果
                            if data.get("type") == "final_result":
                                print(f"🏁 收到最终结果，停止监听")
                                break
                                
                        except json.JSONDecodeError:
                            print(f"⚠️  无法解析JSON: {data_str}")
                    
                    # 防止无限等待
                    if time.time() - start_time > 120:  # 2分钟超时
                        print(f"⏰ 超时停止监听")
                        break
                
                total_time = time.time() - start_time
                print(f"\n📈 进度监听总结:")
                print(f"   - 总进度更新数: {progress_count}")
                print(f"   - 总耗时: {total_time:.2f}秒")
                
            else:
                print(f"❌ 进度流连接失败: {response.status}")
                
    except Exception as e:
        print(f"❌ 进度流异常: {str(e)}")


async def main():
    """主函数"""
    print("=" * 60)
    print("🧪 异步调用修复测试")
    print("=" * 60)
    
    # 检查服务器是否运行
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8080/api/health") as response:
                if response.status == 200:
                    print("✅ 服务器运行正常")
                else:
                    print(f"❌ 服务器状态异常: {response.status}")
                    return
    except Exception as e:
        print(f"❌ 无法连接到服务器: {str(e)}")
        print("请确保服务器正在运行在 http://localhost:8080")
        return
    
    # 执行测试
    await test_async_generation()
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())