#!/usr/bin/env python3
"""
修复后的API测试
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
backend_dir = Path(__file__).parent
src_dir = backend_dir / "src"
sys.path.insert(0, str(src_dir))

from fastapi import FastAPI
from pydantic import BaseModel, Field
from typing import Optional, List
import uvicorn

# 简化的响应模型
class SimpleResponse(BaseModel):
    success: bool
    message: str
    error: Optional[str] = None
    data: Optional[dict] = None

# 请求模型
class SimpleRequest(BaseModel):
    user_input: str
    style_preference: str = "anime"
    num_panels: int = 4

# 创建FastAPI应用
app = FastAPI(title="Fixed Illustration API")

@app.post("/api/test-fixed")
async def test_fixed_endpoint(request: SimpleRequest):
    """修复后的测试端点"""
    try:
        # 模拟处理
        result_data = {
            "processed_story": f"处理后的故事: {request.user_input}",
            "characters": [{"name": "测试角色", "role": "main"}],
            "storyboards": [{"panel_id": 1, "description": "测试分镜"}],
            "generated_images": [],
            "final_illustration": None
        }
        
        return SimpleResponse(
            success=True,
            message="测试成功",
            data=result_data
        )
        
    except Exception as e:
        return SimpleResponse(
            success=False,
            message="测试失败",
            error=str(e)
        )

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}

if __name__ == "__main__":
    print("🧪 启动修复后的API测试服务器")
    print("📍 地址: http://localhost:8081")
    uvicorn.run(app, host="0.0.0.0", port=8081, log_level="info")
