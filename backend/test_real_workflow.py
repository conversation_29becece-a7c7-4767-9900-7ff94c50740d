#!/usr/bin/env python3
"""
真实工作流测试脚本

测试真实LLM工作流是否能正常执行和推送进度
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime


async def test_real_workflow():
    """测试真实工作流执行"""
    print("🧪 测试真实工作流执行")
    
    # 简单的测试故事，避免太复杂导致LLM处理时间过长
    test_data = {
        "user_input": "一个小猫咪找到了一颗闪亮的星星",
        "style_preference": "anime", 
        "num_panels": 2  # 减少分镜数量以加快测试
    }
    
    print(f"📝 测试数据: {test_data}")
    
    async with aiohttp.ClientSession() as session:
        print(f"⏰ 开始时间: {datetime.now().strftime('%H:%M:%S')}")
        
        try:
            # 步骤1: 启动生成
            print(f"\n📤 发送启动请求...")
            start_time = time.time()
            
            async with session.post(
                "http://localhost:8080/api/start-generation",
                json=test_data,
                headers={"Platform": "Web"}
            ) as response:
                response_time = time.time() - start_time
                print(f"📬 启动响应时间: {response_time:.2f}秒")
                
                if response.status == 200:
                    result = await response.json()
                    session_id = result.get("session_id")
                    print(f"✅ 启动成功，会话ID: {session_id}")
                    
                    if session_id:
                        # 步骤2: 监听真实工作流进度
                        await monitor_real_workflow_progress(session, session_id)
                    else:
                        print("❌ 未获得会话ID")
                else:
                    text = await response.text()
                    print(f"❌ 启动失败: {response.status} - {text}")
                    
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")


async def monitor_real_workflow_progress(session, session_id):
    """监听真实工作流进度"""
    print(f"\n📡 开始监听真实工作流进度: {session_id}")
    
    workflow_stats = {
        'total_updates': 0,
        'llm_calls': 0,
        'image_progress': 0,
        'start_time': time.time(),
        'real_workflow': False,
        'failover_detected': False
    }
    
    try:
        async with session.get(
            f"http://localhost:8080/api/progress/{session_id}",
            headers={"Platform": "Web"}
        ) as response:
            
            if response.status != 200:
                print(f"❌ 进度流连接失败: {response.status}")
                return
            
            print(f"📊 进度流已连接，等待真实工作流执行...")
            
            async for line in response.content:
                if line.startswith(b'data: '):
                    workflow_stats['total_updates'] += 1
                    data_str = line[6:].decode('utf-8').strip()
                    
                    try:
                        data = json.loads(data_str)
                        current_time = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        
                        # 分析数据来源
                        if data.get("type") == "progress":
                            node_name = data.get('node_name', 'unknown')
                            status = data.get('status', 'unknown')
                            title = data.get('title', 'unknown')
                            description = data.get('description', 'unknown')
                            
                            # 检测是否为真实工作流
                            if "LLM" in description or "模型" in description or "调用" in description:
                                workflow_stats['llm_calls'] += 1
                                workflow_stats['real_workflow'] = True
                                print(f"🤖 [{current_time}] 真实LLM调用: {title} - {description}")
                            elif node_name == "image_generation_progress":
                                workflow_stats['image_progress'] += 1
                                print(f"🎨 [{current_time}] 图像进度: {description}")
                            elif "模拟" in description or "mock://" in str(data.get('data', '')):
                                workflow_stats['failover_detected'] = True
                                print(f"🔄 [{current_time}] Failover模式: {title} - {description}")
                            else:
                                status_icon = "🔄" if status == "in_progress" else "✅" if status == "completed" else "❓"
                                print(f"{status_icon} [{current_time}] {title} - {description}")
                        
                        elif data.get("type") == "result":
                            print(f"🏁 [{current_time}] 收到最终结果!")
                            result_data = data.get('data', {})
                            
                            # 判断结果来源
                            final_illustration = result_data.get('final_illustration', '')
                            if 'mock://' in final_illustration:
                                print(f"   📋 结果来源: Failover模式（模拟数据）")
                            else:
                                print(f"   📋 结果来源: 真实工作流")
                            
                            print(f"   - 最终插画: {final_illustration}")
                            print(f"   - 处理后故事: {result_data.get('processed_story', '')[:50]}...")
                            print(f"   - 角色数量: {len(result_data.get('characters', []))}")
                            print(f"   - 分镜数量: {len(result_data.get('storyboards', []))}")
                            print(f"   - 生成图片: {len(result_data.get('generated_images', []))}")
                            break
                            
                        elif data.get("type") == "error":
                            print(f"❌ [{current_time}] 收到错误: {data.get('message')}")
                            break
                            
                    except json.JSONDecodeError:
                        print(f"⚠️  JSON解析失败: {data_str[:100]}...")
                
                # 防止过长等待（真实工作流可能需要更长时间）
                if time.time() - workflow_stats['start_time'] > 300:  # 5分钟超时
                    print(f"⏰ 超时停止监听（5分钟）")
                    break
    
        # 分析结果
        total_time = time.time() - workflow_stats['start_time']
        print(f"\n📈 真实工作流测试统计:")
        print(f"   - 总耗时: {total_time:.2f}秒")
        print(f"   - 总进度更新: {workflow_stats['total_updates']}")
        print(f"   - LLM调用检测: {workflow_stats['llm_calls']}")
        print(f"   - 图像进度更新: {workflow_stats['image_progress']}")
        print(f"   - 真实工作流执行: {'✅' if workflow_stats['real_workflow'] else '❌'}")
        print(f"   - Failover模式触发: {'✅' if workflow_stats['failover_detected'] else '❌'}")
        
        # 测试结果评估
        if workflow_stats['real_workflow'] and not workflow_stats['failover_detected']:
            print(f"\n✅ 测试成功：真实工作流正常执行")
        elif workflow_stats['failover_detected'] and not workflow_stats['real_workflow']:
            print(f"\n⚠️  测试结果：真实工作流失败，Failover正常工作")
        elif workflow_stats['real_workflow'] and workflow_stats['failover_detected']:
            print(f"\n🔄 测试结果：真实工作流部分执行，然后触发Failover")
        else:
            print(f"\n❓ 测试结果：无法确定工作流执行状态")
            
    except Exception as e:
        print(f"❌ 进度监听异常: {str(e)}")


async def check_llm_configuration():
    """检查LLM配置"""
    print("🔧 检查LLM配置...")
    
    try:
        # 检查环境变量
        import os
        qwen_key = os.getenv("QWEN_API_KEY", "未设置")
        qwen_url = os.getenv("QWEN_BASE_URL", "未设置")
        
        print(f"   - QWEN_API_KEY: {'已设置' if qwen_key != '未设置' and qwen_key != 'your-qwen-api-key' else '未正确设置'}")
        print(f"   - QWEN_BASE_URL: {qwen_url}")
        
        # 尝试简单的LLM调用测试
        import sys
        import os
        sys.path.insert(0, 'src')
        
        from agent.llm_utils import get_default_llm
        
        print(f"   - 尝试初始化LLM...")
        llm = get_default_llm(temperature=0.1)
        print(f"   - LLM初始化成功: {type(llm)}")
        
        # 简单测试调用
        print(f"   - 测试LLM调用...")
        response = llm.invoke("回答'OK'即可")
        print(f"   - LLM调用成功: {response.content[:50]}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ LLM配置检查失败: {str(e)}")
        return False


async def main():
    print("=" * 60)
    print("🧪 真实工作流测试")
    print("=" * 60)
    
    # 检查后端服务器
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8080/api/health") as response:
                if response.status == 200:
                    health_data = await response.json()
                    print(f"✅ 后端服务器健康: {health_data}")
                else:
                    print(f"❌ 后端服务器异常: {response.status}")
                    return
    except Exception as e:
        print(f"❌ 无法连接后端服务器: {str(e)}")
        return
    
    # 检查LLM配置
    if not await check_llm_configuration():
        print(f"❌ LLM配置不正确，可能影响真实工作流执行")
        print(f"提示: 请检查QWEN_API_KEY是否正确设置")
    
    print()
    # 执行主测试
    await test_real_workflow()
    
    print("\n" + "=" * 60)
    print("🏁 真实工作流测试完成")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())