"""插画生成Agent的测试脚本"""

import asyncio
import json
from typing import Dict, Any

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from agent.illustration_graph import generate_illustration_sync
from agent.configuration import Configuration


def test_story_processing():
    """测试故事处理功能"""
    print("=== 测试故事处理功能 ===")
    
    test_cases = [
        {
            "name": "简短故事扩写",
            "input": "小猫咪迷路了",
            "style": "anime",
            "panels": 4
        },
        {
            "name": "完整故事优化",
            "input": "在一个阳光明媚的下午，小女孩艾米在公园里发现了一只受伤的小鸟。她小心翼翼地把小鸟带回家，给它包扎伤口，喂它食物。经过几天的精心照料，小鸟恢复了健康。艾米把小鸟放回了大自然，小鸟在天空中快乐地飞翔。",
            "style": "watercolor",
            "panels": 4
        },
        {
            "name": "科幻故事",
            "input": "2050年，机器人助手小AI帮助人类探索火星",
            "style": "cyberpunk",
            "panels": 6
        }
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n--- 测试用例 {i+1}: {case['name']} ---")
        print(f"输入: {case['input']}")
        print(f"风格: {case['style']}")
        print(f"分镜数: {case['panels']}")
        
        try:
            # 运行插画生成（仅测试到故事处理阶段）
            result = test_single_node_story_processing(
                case['input'], 
                case['style'], 
                case['panels']
            )
            
            print(f"✅ 处理成功")
            print(f"处理后故事: {result.get('processed_story', 'N/A')[:100]}...")
            print(f"故事类型: {result.get('story_type', 'N/A')}")
            
        except Exception as e:
            print(f"❌ 处理失败: {str(e)}")


def test_single_node_story_processing(user_input: str, style: str, panels: int) -> Dict[str, Any]:
    """测试单个节点：故事处理"""
    from agent.illustration_nodes import input_handler
    from agent.state import IllustrationState
    from langchain_core.runnables import RunnableConfig
    
    # 构建测试状态
    state = {
        "user_input": user_input,
        "style_preference": style,
        "num_panels": panels,
        "messages": []
    }
    
    # 构建配置
    config = RunnableConfig(configurable={})
    
    # 调用节点
    result = input_handler(state, config)
    return result


def test_character_extraction():
    """测试角色提取功能"""
    print("\n=== 测试角色提取功能 ===")
    
    test_story = """
    在魔法学院里，年轻的魔法师艾莉丝正在学习新的咒语。她有着金色的长发和蓝色的眼睛，
    总是穿着蓝色的魔法袍。她的好朋友汤姆是一个勇敢的骑士，身穿银色盔甲，手持长剑。
    他们一起踏上了拯救被困公主的冒险之旅。
    """
    
    try:
        from agent.illustration_nodes import character_extractor
        from langchain_core.runnables import RunnableConfig
        
        state = {
            "processed_story": test_story,
            "style_preference": "anime"
        }
        
        config = RunnableConfig(configurable={})
        result = character_extractor(state, config)
        
        print("✅ 角色提取成功")
        print(f"角色数量: {len(result.get('characters', []))}")
        
        for char in result.get('characters', []):
            print(f"- {char['name']}: {char['role']} | {char['appearance'][:50]}...")
            
        if result.get('main_character'):
            print(f"主角: {result['main_character']['name']}")
            
    except Exception as e:
        print(f"❌ 角色提取失败: {str(e)}")


def test_full_workflow():
    """测试完整工作流"""
    print("\n=== 测试完整工作流 ===")
    
    test_input = "小红帽去森林里看望奶奶，路上遇到了大灰狼"
    
    print(f"输入故事: {test_input}")
    print("开始生成插画...")
    
    try:
        result = generate_illustration_sync(
            user_input=test_input,
            style_preference="anime",
            num_panels=4
        )
        
        print("✅ 工作流执行完成")
        
        # 检查各个阶段的结果
        if result.get("processed_story"):
            print(f"✅ 故事处理: {result['processed_story'][:100]}...")
        
        if result.get("characters"):
            print(f"✅ 角色提取: {len(result['characters'])} 个角色")
        
        if result.get("storyboards"):
            print(f"✅ 分镜生成: {len(result['storyboards'])} 个分镜")
        
        if result.get("scene_prompts"):
            print(f"✅ 提示词优化: {len(result['scene_prompts'])} 个提示词")
        
        if result.get("generated_images"):
            successful_images = [img for img in result['generated_images'] if img.get('success')]
            print(f"✅ 图像生成: {len(successful_images)}/{len(result['generated_images'])} 成功")
        
        if result.get("final_illustration"):
            print("✅ 最终插画: 生成成功")
        else:
            print("⚠️ 最终插画: 未生成")
        
        # 显示消息
        messages = result.get("messages", [])
        if messages:
            final_message = messages[-1]
            if hasattr(final_message, 'content'):
                print(f"结果消息: {final_message.content}")
        
        return result
        
    except Exception as e:
        print(f"❌ 工作流执行失败: {str(e)}")
        return None


def test_api_endpoint():
    """测试API端点"""
    print("\n=== 测试API端点 ===")
    
    try:
        import requests
        
        # 测试健康检查
        response = requests.get("http://localhost:8080/api/health")
        if response.status_code == 200:
            print("✅ 健康检查通过")
        else:
            print("❌ 健康检查失败")
            return
        
        # 测试风格列表
        response = requests.get("http://localhost:8080/api/styles")
        if response.status_code == 200:
            styles = response.json()
            print(f"✅ 风格列表获取成功: {len(styles['styles'])} 种风格")
        else:
            print("❌ 风格列表获取失败")
        
        # 测试插画生成
        test_data = {
            "user_input": "小猫咪在花园里玩耍",
            "style_preference": "anime",
            "num_panels": 2
        }
        
        response = requests.post(
            "http://localhost:8080/api/generate-illustration",
            json=test_data,
            timeout=120  # 2分钟超时
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ 插画生成API测试成功")
            else:
                print(f"❌ 插画生成失败: {result.get('error')}")
        else:
            print(f"❌ API请求失败: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")


def main():
    """主测试函数"""
    print("🚀 开始测试插画生成Agent")
    print("=" * 50)
    
    # 测试各个组件
    test_story_processing()
    test_character_extraction()
    
    # 测试完整工作流
    result = test_full_workflow()
    
    # 如果有结果，保存到文件
    if result:
        with open("test_result.json", "w", encoding="utf-8") as f:
            # 处理不可序列化的对象
            serializable_result = {}
            for key, value in result.items():
                if key == "messages":
                    serializable_result[key] = [
                        {"content": msg.content if hasattr(msg, 'content') else str(msg)}
                        for msg in value
                    ]
                else:
                    serializable_result[key] = value
            
            json.dump(serializable_result, f, ensure_ascii=False, indent=2)
        print("\n📄 测试结果已保存到 test_result.json")
    
    # 测试API（可选）
    print("\n" + "=" * 50)
    print("如果API服务器正在运行，将测试API端点...")
    test_api_endpoint()
    
    print("\n🎉 测试完成！")


if __name__ == "__main__":
    main()
