#!/usr/bin/env python3
"""
测试API调用
"""

import requests
import json

def test_api_call():
    """测试API调用"""
    url = "http://localhost:8080/api/generate-illustration"
    
    # 测试数据
    test_data = {
        "user_input": "小猫咪在花园里玩耍",
        "style_preference": "anime",
        "num_panels": 2
    }
    
    print("🧪 测试插画生成API")
    print(f"📍 URL: {url}")
    print(f"📋 请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    print("-" * 50)
    
    try:
        # 发送POST请求
        response = requests.post(
            url,
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=120  # 2分钟超时
        )
        
        print(f"📊 状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        print("-" * 50)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功")
            print(f"📋 响应数据:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            # 检查响应字段
            if result.get("success"):
                print("\n✅ 插画生成成功")
                if result.get("processed_story"):
                    print(f"📖 处理后故事: {result['processed_story'][:100]}...")
                if result.get("characters"):
                    print(f"👥 角色数量: {len(result['characters'])}")
                if result.get("storyboards"):
                    print(f"🎬 分镜数量: {len(result['storyboards'])}")
                if result.get("generated_images"):
                    print(f"🖼️ 生成图片数量: {len(result['generated_images'])}")
            else:
                print(f"\n❌ 插画生成失败: {result.get('error', 'Unknown error')}")
        else:
            print(f"❌ API调用失败")
            print(f"📄 响应内容: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时")
    except requests.exceptions.ConnectionError:
        print("🔌 连接错误，请确保服务器正在运行")
    except Exception as e:
        print(f"💥 异常: {str(e)}")

if __name__ == "__main__":
    test_api_call()
