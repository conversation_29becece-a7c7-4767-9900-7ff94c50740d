"""图像生成工具模块"""

import os
import base64
import requests
from typing import Optional, Dict, Any, List
from io import BytesIO
import asyncio
from dotenv import load_dotenv

try:
    from PIL import Image
    print(f"✅ PIL Image 导入成功: {Image}")
except ImportError as e:
    print(f"Warning: PIL not installed. Image processing features may not work. Error: {e}")
    Image = None

load_dotenv()


class ImageGenerationAPI:
    """图像生成API客户端"""
    
    def __init__(
        self,
        api_url: str = None,
        api_key: str = None,
        timeout: int = 60
    ):
        self.api_url = api_url or os.getenv("IMAGE_API_URL", "http://localhost:8000")
        self.api_key = api_key or os.getenv("IMAGE_API_KEY", "")
        self.timeout = timeout
        
    def generate_text_to_image(
        self,
        prompt: str,
        style: str = "anime",
        width: int = 512,
        height: int = 512,
        steps: int = 20,
        **kwargs
    ) -> Dict[str, Any]:
        """
        文生图API调用
        
        Args:
            prompt: 提示词
            style: 风格
            width: 图片宽度
            height: 图片高度
            steps: 生成步数
            **kwargs: 其他参数
            
        Returns:
            包含图片URL或base64数据的字典
        """
        payload = {
            "prompt": prompt,
            "style": style,
            "width": width,
            "height": height,
            "steps": steps,
            "type": "text2img",
            **kwargs
        }
        
        headers = {}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
            
        try:
            response = requests.post(
                f"{self.api_url}/generate",
                json=payload,
                headers=headers,
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "image_url": None
            }
    
    def generate_image_to_image(
        self,
        prompt: str,
        reference_image: str,
        style: str = "anime",
        strength: float = 0.7,
        steps: int = 20,
        **kwargs
    ) -> Dict[str, Any]:
        """
        图生图API调用
        
        Args:
            prompt: 提示词
            reference_image: 参考图片URL或base64
            style: 风格
            strength: 变化强度
            steps: 生成步数
            **kwargs: 其他参数
            
        Returns:
            包含图片URL或base64数据的字典
        """
        payload = {
            "prompt": prompt,
            "reference_image": reference_image,
            "style": style,
            "strength": strength,
            "steps": steps,
            "type": "img2img",
            **kwargs
        }
        
        headers = {}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
            
        try:
            response = requests.post(
                f"{self.api_url}/generate",
                json=payload,
                headers=headers,
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "image_url": None
            }
    
    async def generate_batch_images(
        self,
        requests_list: List[Dict[str, Any]],
        max_concurrent: int = 3
    ) -> List[Dict[str, Any]]:
        """
        批量生成图片
        
        Args:
            requests_list: 请求列表
            max_concurrent: 最大并发数
            
        Returns:
            生成结果列表
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def generate_single(request_data):
            async with semaphore:
                if request_data.get("type") == "img2img":
                    return self.generate_image_to_image(**request_data)
                else:
                    return self.generate_text_to_image(**request_data)
        
        tasks = [generate_single(req) for req in requests_list]
        return await asyncio.gather(*tasks)


class ImageProcessor:
    """图像处理工具"""
    
    @staticmethod
    def merge_images_grid(
        image_urls: List[str],
        grid_size: tuple = (2, 2),
        output_size: tuple = (1024, 1024)
    ) -> Optional[str]:
        """
        将多张图片合并为网格布局
        
        Args:
            image_urls: 图片URL列表
            grid_size: 网格大小 (rows, cols)
            output_size: 输出尺寸
            
        Returns:
            合并后的图片base64字符串
        """
        # 检查PIL库是否可用
        if Image is None:
            print("PIL library not available. Using fallback merge strategy.")
            # Failover: 返回第一个有效图片或模拟结果
            for url in image_urls:
                if url and url.startswith('mock://'):
                    # 返回一个模拟的合并图片
                    return "mock://merged_illustration.jpg"
            # 如果没有mock URL，返回第一个图片
            if image_urls:
                return image_urls[0]
            return None
            
        try:
            if not image_urls:
                print("No image URLs provided for merging")
                return None
                
            images = []
            for url in image_urls:
                if not url:  # 检查空 URL
                    print(f"Skipping empty URL: {url}")
                    continue
                    
                if url.startswith('data:image'):
                    # base64图片
                    try:
                        image_data = base64.b64decode(url.split(',')[1])
                        img = Image.open(BytesIO(image_data))
                    except Exception as e:
                        print(f"Failed to decode base64 image: {e}")
                        continue
                else:
                    # URL图片
                    try:
                        response = requests.get(url, timeout=10)
                        response.raise_for_status()
                        img = Image.open(BytesIO(response.content))
                    except Exception as e:
                        print(f"Failed to load image from URL {url}: {e}")
                        continue
                        
                images.append(img)
            
            if not images:
                print("No valid images to merge")
                return None
                
            rows, cols = grid_size
            cell_width = output_size[0] // cols
            cell_height = output_size[1] // rows
            
            merged_image = Image.new('RGB', output_size, 'white')
            
            for i, img in enumerate(images[:rows * cols]):
                row = i // cols
                col = i % cols
                
                # 调整图片大小
                img_resized = img.resize((cell_width, cell_height), Image.Resampling.LANCZOS)
                
                # 粘贴到合并图片
                x = col * cell_width
                y = row * cell_height
                merged_image.paste(img_resized, (x, y))
            
            # 转换为base64
            buffer = BytesIO()
            merged_image.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            return f"data:image/png;base64,{img_str}"
            
        except Exception as e:
            print(f"Error merging images: {e}")
            return None
    
    @staticmethod
    def resize_image(image_url: str, size: tuple = (512, 512)) -> Optional[str]:
        """
        调整图片大小
        
        Args:
            image_url: 图片URL或base64
            size: 目标尺寸
            
        Returns:
            调整后的图片base64字符串
        """
        try:
            if image_url.startswith('data:image'):
                image_data = base64.b64decode(image_url.split(',')[1])
                img = Image.open(BytesIO(image_data))
            else:
                response = requests.get(image_url)
                img = Image.open(BytesIO(response.content))
            
            img_resized = img.resize(size, Image.Resampling.LANCZOS)
            
            buffer = BytesIO()
            img_resized.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            return f"data:image/png;base64,{img_str}"
            
        except Exception as e:
            print(f"Error resizing image: {e}")
            return None


# 全局图像生成客户端实例
image_api = ImageGenerationAPI()
image_processor = ImageProcessor()
