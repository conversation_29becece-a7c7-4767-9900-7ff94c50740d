"""插画生成Agent的提示词模板"""

from datetime import datetime


def get_current_date():
    """获取当前日期"""
    return datetime.now().strftime("%B %d, %Y")


# 翻译提示词
translation_instructions = """你是一个专业的翻译专家，需要将中文内容翻译成适合AI绘画的英文描述。

原始内容：{original_text}

请将上述内容翻译成英文，要求：

1. **保持故事完整性**：确保翻译后的内容保持原有的故事结构和情节
2. **适合AI绘画**：使用具体、生动的英文描述，便于AI图像生成
3. **准确传达**：准确传达原文的意思和情感
4. **自然流畅**：译文要自然流畅，符合英文表达习惯

翻译要求：
- 保持专有名词的一致性
- 使用丰富的视觉描述词汇
- 避免过于复杂的句式
- 确保适合分镜制作

请直接返回纯JSON格式的结果，不要使用markdown代码块包装，包含以下字段：
- original_text：原始文本
- translated_text：翻译后的文本
- source_language：源语言（固定为"chinese"）
- target_language：目标语言（固定为"english"）

重要：请直接返回JSON对象，不要使用```json```代码块包装。"""


# 故事处理提示词
story_processing_instructions = """你是一个专业的故事编辑和创作助手。根据用户输入的内容，你需要判断是否需要扩写或总结，并进行相应的处理。

当前日期：{current_date}

用户输入：{user_input}

请分析用户输入的内容，并按照以下规则处理：

1. **如果输入内容较短或过于简单**（少于100字或缺乏具体情节）：
   - 进行扩写，添加更多细节、情节发展和角色描述
   - 确保故事有完整的开头、发展和结尾
   - 添加适合视觉化的场景描述

2. **如果输入内容较长或过于复杂**（超过500字或情节过于复杂）：
   - 进行总结，保留核心情节和关键角色
   - 简化复杂的情节线，突出主要冲突和转折点
   - 确保适合分镜展示

3. **如果输入内容适中**：
   - 进行适度优化，增强视觉化描述
   - 确保故事结构清晰，适合分镜制作

输出要求：
请直接返回纯JSON格式的结果，不要使用markdown代码块包装，包含以下字段：
- processed_story：处理后的故事内容（200-400字）
- processed_story_en：处理后的故事内容英文版本（适合AI绘画的英文描述）
- story_type：处理类型（"summary"、"expansion"或"optimization"）
- rationale：处理决策的理由

风格要求：
- 适合{style_preference}风格的插画
- 包含丰富的视觉元素描述
- 情节紧凑，适合{num_panels}个分镜展示
- 英文版本要使用生动具体的视觉描述，便于AI图像生成

重要：请直接返回JSON对象，不要使用```json```代码块包装，不要添加任何额外的文本说明。"""


# 故事分段提示词
story_segmentation_instructions = """你是一个专业的分镜师，需要将故事分解为适合插画展示的分段。

故事内容：{story_content}
目标分段数：{num_segments}
风格偏好：{style_preference}

请将故事分解为{num_segments}个分段，每个分段应该：

1. **包含一个明确的场景或情节点**
2. **适合单独制作插画**
3. **有清晰的视觉焦点**
4. **推进整体故事发展**

分段要求：
- 每个分段50-80字
- 包含场景、角色、动作的描述
- 突出关键的视觉元素
- 保持故事的连贯性和节奏感

输出格式：
请直接返回纯JSON格式的结果，不要使用markdown代码块包装，包含以下字段：
- segments：分段列表，每个分段包含：
  - segment_id：分段编号
  - content：中文分段内容
  - content_en：英文分段内容（适合AI绘画的生动描述）
  - key_elements：关键元素列表
- total_segments：总分段数

英文版本要求：
- 使用生动具体的英文描述
- 包含丰富的视觉细节
- 适合{style_preference}风格的AI绘画
- 确保与中文版本的意思一致

重要：请直接返回JSON对象，不要使用```json```代码块包装。"""


# 角色提取提示词
character_extraction_instructions = """你是一个专业的角色设计师，需要从故事中提取角色信息并设计角色特征。

故事内容：{story_content}
风格偏好：{style_preference}

请分析故事中的角色，并提取以下信息：

1. **识别所有重要角色**（主角和重要配角）
2. **设计角色的外貌特征**
3. **确定角色的风格设定**
4. **分析角色在故事中的作用**

角色信息要求：
- name：角色名称（如果故事中没有明确名称，请根据角色特点命名）
- description：角色的基本描述和性格特点
- appearance：外貌特征的文字描述（必须是字符串，不要使用嵌套对象）
- style：风格设定的文字描述（必须是字符串，不要使用嵌套对象）
- role：角色类型（"main"表示主角，"supporting"表示配角）

外貌和风格描述要求：
- appearance字段：用一段文字描述角色的外貌特征，包含发型、面部、体型等
- style字段：用一段文字描述角色的服装、配色、配件等风格元素
- 两个字段都必须是完整的字符串描述，适合{style_preference}风格
- 避免版权角色，创造原创设计
- 描述要具体且适合AI绘画生成

输出格式：
请直接返回纯JSON格式的结果，不要使用markdown代码块包装，包含以下字段：
- characters：角色列表
- main_character：主角信息（如果有明确主角的话）

重要：请直接返回JSON对象，不要使用```json```代码块包装。"""


# 分镜生成提示词
storyboard_generation_instructions = """你是一个专业的分镜师，需要将故事分段转换为详细的分镜描述。

故事分段：{story_segments}
角色信息：{characters_info}
风格偏好：{style_preference}

请为每个故事分段创建详细的分镜描述，包括：

1. **场景描述**：环境、背景、氛围
2. **角色表现**：涉及的角色、动作、表情
3. **镜头设计**：角度、构图、焦点
4. **视觉元素**：色彩、光影、特效

分镜要求：
- 每个分镜要有明确的视觉焦点
- 适合{style_preference}风格的表现
- 考虑角色的一致性设计
- 包含丰富的视觉细节

输出格式：
请直接返回纯JSON格式的结果，不要使用markdown代码块包装，包含以下字段：
每个分镜包含：
- panel_id：分镜编号（必须是整数，如1、2、3，不要使用字符串如"Panel_01"）
- scene_description：场景描述
- characters_involved：涉及的角色名称列表
- environment：环境描述
- action：动作描述
- mood：氛围描述
- camera_angle：镜头角度（如"正面视角"、"俯视"、"特写"等）

整体要求：
- panels：分镜列表
- style_notes：整体风格说明

重要：panel_id字段必须是数字类型（1, 2, 3...），请直接返回JSON对象，不要使用```json```代码块包装。"""


# 提示词优化指令
prompt_optimization_instructions = """你是一个专业的AI绘画提示词工程师，需要将分镜描述转换为高质量的英文AI绘画提示词。

分镜信息：{storyboard_panels}
角色信息：{characters_info}
风格偏好：{style_preference}

请为每个分镜生成优化的提示词，要求：

1. **使用英文提示词**：生成的提示词应该是英文的，适合AI绘画模型
2. **使用第三人称客观描述**
3. **包含具体的视觉元素**
4. **适合AI绘画模型理解**
5. **保持角色一致性**

提示词结构：
- 主体描述（角色、动作）
- 环境描述（背景、场景）
- 风格标签（艺术风格、画面质量）
- 技术参数（构图、光影等）

风格适配：
- {style_preference}风格的具体表现
- 高质量画面标签
- 避免负面元素

输出格式：
请直接返回纯JSON格式的结果，不要使用markdown代码块包装，包含以下字段：
- prompts：提示词列表，每个提示词包含：
  - panel_id：对应的分镜ID
  - prompt：优化后的完整提示词
  - style_tags：风格标签列表
  - generation_type：生成类型（"text2img"或"img2img"）

注意事项：
- 如果是主角出现的场景，generation_type设为"img2img"（使用角色基准图）
- 如果是环境场景或配角场景，generation_type设为"text2img"
- 提示词要具体、准确，避免模糊描述

重要：请直接返回JSON对象，必须包含prompts字段，不要使用```json```代码块包装。"""
