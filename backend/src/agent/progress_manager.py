"""进度管理器 - 管理插画生成的实时进度"""

import time
from typing import Dict, List, Any, Optional, Callable
from threading import Lock
from dataclasses import dataclass
from enum import Enum


class NodeStatus(Enum):
    """节点执行状态"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class ProgressUpdate:
    """进度更新数据结构"""
    type: str  # "progress", "result", "error"
    node_name: str
    status: str
    title: str
    description: str
    timestamp: float
    data: Any = None


class SessionProgress:
    """单个会话的进度状态"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.started_at = time.time()
        self.completed = False
        self.has_error = False
        self.error_message = ""
        self.final_result = None
        self.progress_updates: List[ProgressUpdate] = []
        self.pending_updates: List[ProgressUpdate] = []
        self.current_nodes: Dict[str, str] = {}  # node_name -> status
        self.progress_callback: Optional[Callable] = None
        
    def add_progress_update(self, node_name: str, status: str, data: Any = None):
        """添加进度更新"""
        # 处理特殊的进度消息格式
        title = self._get_node_title(node_name)
        
        # 如果是图像生成进度且data是字符串，则使用data作为描述
        if node_name == 'image_generation_progress' and isinstance(data, str):
            description = data
        else:
            description = self._get_node_description(node_name, status)
        
        update = ProgressUpdate(
            type="progress",
            node_name=node_name,
            status=status,
            title=title,
            description=description,
            timestamp=time.time(),
            data=data
        )
        
        self.progress_updates.append(update)
        self.pending_updates.append(update)
        self.current_nodes[node_name] = status
        
    def _get_node_title(self, node_name: str) -> str:
        """获取节点标题"""
        titles = {
            'input_handler': '📝 故事处理',
            'story_splitter': '📚 故事分段', 
            'character_extractor': '👥 角色提取',
            'character_image_generator': '🎨 角色基准图生成',
            'storyboard_generator': '🎬 分镜生成',
            'scene_prompt_optimizer': '✨ 提示词优化',
            'unified_image_generator': '🖼️ 图像生成',
            'image_generation_progress': '🎨 图像生成进度',  # 新增
            'image_merger': '🔗 图像合并',
            'finalize_illustration': '🎉 完成'
        }
        return titles.get(node_name, node_name)
        
    def _get_node_description(self, node_name: str, status: str) -> str:
        """获取节点描述"""
        # 特殊处理图像生成进度
        if node_name == 'image_generation_progress' and status == 'progress':
            return "图像生成中..."
            
        if status == NodeStatus.IN_PROGRESS.value:
            descriptions = {
                'input_handler': '正在分析和优化您的故事内容...',
                'story_splitter': '正在将故事分解为适合插画的分段...',
                'character_extractor': '正在识别故事中的角色并提取特征...',
                'character_image_generator': '正在生成角色基准图以保持一致性...',
                'storyboard_generator': '正在创建详细的分镜描述...',
                'scene_prompt_optimizer': '正在优化AI绘画提示词...',
                'unified_image_generator': '正在生成插画图片...',
                'image_merger': '正在合并图片为最终插画...',
                'finalize_illustration': '正在最终化插画结果...'
            }
            return descriptions.get(node_name, '处理中...')
        elif status == NodeStatus.COMPLETED.value:
            title = self._get_node_title(node_name)
            # 安全地获取标题名称（去掉emoji）
            if ' ' in title:
                clean_title = title.split(' ', 1)[1]  # 去掉emoji
            else:
                clean_title = title
            return f"{clean_title}完成"
        elif status == NodeStatus.ERROR.value:
            title = self._get_node_title(node_name)
            # 安全地获取标题名称（去掉emoji）
            if ' ' in title:
                clean_title = title.split(' ', 1)[1]  # 去掉emoji
            else:
                clean_title = title
            return f"{clean_title}出错"
        else:
            return "等待执行..."
            
    def get_pending_updates(self) -> List[Dict]:
        """获取待推送的更新"""
        updates = []
        for update in self.pending_updates:
            updates.append({
                "type": update.type,
                "node_name": update.node_name,
                "status": update.status,
                "title": update.title,
                "description": update.description,
                "timestamp": update.timestamp,
                "data": update.data
            })
        
        # 清空待推送列表
        self.pending_updates.clear()
        return updates
        
    def get_all_progress_updates(self) -> List[Dict]:
        """获取所有进度更新（不清空待推送列表）"""
        updates = []
        for update in self.progress_updates:
            updates.append({
                "type": update.type,
                "node_name": update.node_name,
                "status": update.status,
                "title": update.title,
                "description": update.description,
                "timestamp": update.timestamp,
                "data": update.data
            })
        return updates
        
    def set_completed(self, result: Any):
        """设置完成状态"""
        self.completed = True
        self.final_result = result
        
    def set_error(self, error_message: str):
        """设置错误状态"""
        self.has_error = True
        self.error_message = error_message


class ProgressManager:
    """全局进度管理器"""
    
    def __init__(self):
        self.sessions: Dict[str, SessionProgress] = {}
        self.lock = Lock()
        
    def start_session(self, session_id: str):
        """开始新的会话"""
        with self.lock:
            self.sessions[session_id] = SessionProgress(session_id)
            
    def session_exists(self, session_id: str) -> bool:
        """检查会话是否存在"""
        with self.lock:
            return session_id in self.sessions
            
    def update_progress(self, session_id: str, node_name: str, status: str, data: Any = None):
        """更新进度"""
        with self.lock:
            if session_id in self.sessions:
                self.sessions[session_id].add_progress_update(node_name, status, data)
                
    def get_progress_updates(self, session_id: str) -> List[Dict]:
        """获取进度更新"""
        with self.lock:
            if session_id in self.sessions:
                return self.sessions[session_id].get_pending_updates()
            return []
            
    def get_all_progress_updates(self, session_id: str) -> List[Dict]:
        """获取所有进度更新（不清空待推送列表）"""
        with self.lock:
            if session_id in self.sessions:
                return self.sessions[session_id].get_all_progress_updates()
            return []
            
    def set_progress_callback(self, session_id: str, callback: Callable):
        """设置进度回调"""
        with self.lock:
            if session_id in self.sessions:
                self.sessions[session_id].progress_callback = callback
                
    def is_completed(self, session_id: str) -> bool:
        """检查是否完成"""
        with self.lock:
            if session_id in self.sessions:
                return self.sessions[session_id].completed
            return False
            
    def has_error(self, session_id: str) -> bool:
        """检查是否有错误"""
        with self.lock:
            if session_id in self.sessions:
                return self.sessions[session_id].has_error
            return False
            
    def get_final_result(self, session_id: str) -> Optional[Dict]:
        """获取最终结果"""
        with self.lock:
            if session_id in self.sessions and self.sessions[session_id].completed:
                result = self.sessions[session_id].final_result
                print(f"DEBUG: 获取最终结果 - session_id: {session_id}, result: {result is not None}")
                if result:
                    final_result = {
                        "type": "result",
                        "success": "error" not in result,
                        "data": result,
                        "timestamp": time.time()
                    }
                    print(f"DEBUG: 构建的最终结果: {type(final_result)}, success: {final_result['success']}")
                    return final_result
            else:
                print(f"DEBUG: 未找到对应的session或未完成 - session_id: {session_id}")
                if session_id in self.sessions:
                    print(f"DEBUG: session存在，但完成状态: {self.sessions[session_id].completed}")
            return None
            
    def get_error(self, session_id: str) -> Optional[Dict]:
        """获取错误信息"""
        with self.lock:
            if session_id in self.sessions and self.sessions[session_id].has_error:
                return {
                    "type": "error",
                    "message": self.sessions[session_id].error_message,
                    "timestamp": time.time()
                }
            return None
            
    def set_final_result(self, session_id: str, result: Any):
        """设置最终结果"""
        with self.lock:
            if session_id in self.sessions:
                self.sessions[session_id].set_completed(result)
                
    def set_error(self, session_id: str, error_message: str):
        """设置错误"""
        with self.lock:
            if session_id in self.sessions:
                self.sessions[session_id].set_error(error_message)
                
    def cleanup_session(self, session_id: str):
        """清理会话"""
        with self.lock:
            if session_id in self.sessions:
                del self.sessions[session_id]
                
    def cleanup_old_sessions(self, max_age_seconds: int = 3600):
        """清理旧的会话"""
        current_time = time.time()
        with self.lock:
            sessions_to_remove = []
            for session_id, session in self.sessions.items():
                if current_time - session.started_at > max_age_seconds:
                    sessions_to_remove.append(session_id)
                    
            for session_id in sessions_to_remove:
                del self.sessions[session_id]