"""异步图像生成管理器"""

import asyncio
import uuid
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import logging

from agent.remote_image_api import RemoteImageAPI, ModelType, TaskStatus, ImageGenerationTask

logger = logging.getLogger(__name__)


@dataclass
class ImageGenerationRequest:
    """图像生成请求"""
    prompt: str
    style: str
    generation_type: str  # "text2img" or "img2img"
    panel_id: Optional[int] = None
    character_name: Optional[str] = None
    reference_image: Optional[str] = None
    width: int = 768
    height: int = 1344


class AsyncImageManager:
    """异步图像生成管理器"""
    
    def __init__(self):
        self.api = RemoteImageAPI()
        self.style_to_model = {
            "anime": ModelType.ANIME,
            "realistic": ModelType.REALISTIC,
            "art": ModelType.ART,
            "cartoon": ModelType.ANIME,  # 卡通使用动漫模型
            "cyberpunk": ModelType.REALISTIC,  # 赛博朋克使用真实模型
            "watercolor": ModelType.ART,  # 水彩使用艺术模型
            "sketch": ModelType.ART  # 素描使用艺术模型
        }
    
    def _get_model_type(self, style: str) -> ModelType:
        """根据风格获取模型类型"""
        return self.style_to_model.get(style.lower(), ModelType.ANIME)
    
    def _generate_task_id(self, prefix: str = "img") -> str:
        """生成任务ID"""
        return f"{prefix}_{uuid.uuid4().hex[:8]}"
    
    async def generate_character_images(
        self,
        characters: List[Dict[str, Any]],
        style: str,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, str]:
        """
        生成角色基准图
        
        Args:
            characters: 角色信息列表
            style: 艺术风格
            progress_callback: 进度回调函数
            
        Returns:
            角色名称到图片URL的映射
        """
        if not characters:
            return {}
        
        model_type = self._get_model_type(style)
        tasks = []
        task_to_character = {}
        
        # 创建生成任务
        for character in characters:
            if character.get("role") == "main" or len(tasks) < 3:  # 限制生成数量
                task_id = self._generate_task_id("char")
                
                # 构建角色提示词
                prompt = f"{character['appearance']}, {style} style, character reference sheet, full body, white background, high quality, detailed"
                
                # 创建任务
                task = await self.api.create_generation_task(
                    task_id=task_id,
                    prompt=prompt,
                    model_type=model_type,
                    width=512,
                    height=768  # 角色图使用竖向比例
                )
                
                tasks.append(task)
                task_to_character[task_id] = character["name"]
                
                logger.info(f"Created character generation task for {character['name']}: {task_id}")
        
        if not tasks:
            return {}
        
        # 等待任务完成
        task_ids = [task.task_id for task in tasks]
        
        def progress_wrapper(completed: int, total: int):
            if progress_callback:
                progress_callback(f"角色基准图生成: {completed}/{total}")
        
        completed_tasks = await self.api.wait_for_tasks_completion(
            task_ids, 
            progress_callback=progress_wrapper
        )
        
        # 整理结果
        character_images = {}
        for task_id, task in completed_tasks.items():
            character_name = task_to_character.get(task_id)
            if character_name and task.status == TaskStatus.SUCCESS and task.result_urls:
                character_images[character_name] = task.result_urls[0]
                logger.info(f"Character {character_name} image generated: {task.result_urls[0]}")
            elif character_name:
                logger.error(f"Character {character_name} generation failed: {task.error_message}")
        
        return character_images
    
    async def generate_scene_images(
        self,
        scene_prompts: List[Dict[str, Any]],
        character_images: Dict[str, str],
        style: str,
        progress_callback: Optional[Callable] = None
    ) -> List[Dict[str, Any]]:
        """
        生成场景图片
        
        Args:
            scene_prompts: 场景提示词列表
            character_images: 角色基准图映射
            style: 艺术风格
            progress_callback: 进度回调函数
            
        Returns:
            生成结果列表
        """
        if not scene_prompts:
            return []
        
        model_type = self._get_model_type(style)
        tasks = []
        task_to_panel = {}
        
        # 创建生成任务
        for prompt_data in scene_prompts:
            task_id = self._generate_task_id("scene")
            panel_id = prompt_data.get("panel_id", 0)
            prompt = prompt_data.get("prompt", "")
            generation_type = prompt_data.get("generation_type", "text2img")
            
            # 如果是图生图且有角色基准图，这里需要特殊处理
            # 目前先使用文生图，后续可以扩展图生图功能
            
            # 创建任务
            task = await self.api.create_generation_task(
                task_id=task_id,
                prompt=prompt,
                model_type=model_type,
                width=768,
                height=512  # 场景图使用横向比例
            )
            
            tasks.append(task)
            task_to_panel[task_id] = {
                "panel_id": panel_id,
                "prompt": prompt,
                "generation_type": generation_type
            }
            
            logger.info(f"Created scene generation task for panel {panel_id}: {task_id}")
        
        if not tasks:
            return []
        
        # 等待任务完成
        task_ids = [task.task_id for task in tasks]
        
        def progress_wrapper(completed: int, total: int):
            if progress_callback:
                progress_callback(f"场景图片生成: {completed}/{total}")
        
        completed_tasks = await self.api.wait_for_tasks_completion(
            task_ids,
            progress_callback=progress_wrapper
        )
        
        # 整理结果
        generated_images = []
        for task_id, task in completed_tasks.items():
            panel_info = task_to_panel.get(task_id, {})
            panel_id = panel_info.get("panel_id", 0)
            
            result = {
                "panel_id": panel_id,
                "prompt_used": panel_info.get("prompt", ""),
                "generation_type": panel_info.get("generation_type", "text2img"),
                "success": task.status == TaskStatus.SUCCESS,
                "image_url": "",
                "error": ""
            }
            
            if task.status == TaskStatus.SUCCESS and task.result_urls:
                result["image_url"] = task.result_urls[0]
                logger.info(f"Panel {panel_id} image generated: {task.result_urls[0]}")
            else:
                result["error"] = task.error_message
                logger.error(f"Panel {panel_id} generation failed: {task.error_message}")
            
            generated_images.append(result)
        
        # 按panel_id排序
        generated_images.sort(key=lambda x: x["panel_id"])
        
        return generated_images
    
    async def generate_all_images(
        self,
        characters: List[Dict[str, Any]],
        scene_prompts: List[Dict[str, Any]],
        style: str,
        progress_callback: Optional[Callable] = None
    ) -> tuple[Dict[str, str], List[Dict[str, Any]]]:
        """
        生成所有图片（角色基准图 + 场景图片）
        
        Args:
            characters: 角色信息列表
            scene_prompts: 场景提示词列表
            style: 艺术风格
            progress_callback: 进度回调函数
            
        Returns:
            (角色图片映射, 场景图片列表)
        """
        # 先生成角色基准图
        if progress_callback:
            progress_callback("开始生成角色基准图...")
        
        character_images = await self.generate_character_images(
            characters, 
            style, 
            progress_callback
        )
        
        # 再生成场景图片
        if progress_callback:
            progress_callback("开始生成场景图片...")
        
        scene_images = await self.generate_scene_images(
            scene_prompts,
            character_images,
            style,
            progress_callback
        )
        
        if progress_callback:
            progress_callback("图片生成完成！")
        
        return character_images, scene_images
    
    def get_task_status_summary(self) -> Dict[str, Any]:
        """获取任务状态摘要"""
        all_tasks = self.api.get_all_tasks_status()
        
        summary = {
            "total_tasks": len(all_tasks),
            "pending": 0,
            "processing": 0,
            "success": 0,
            "failed": 0,
            "tasks": []
        }
        
        for task_id, task in all_tasks.items():
            summary[task.status.value] += 1
            summary["tasks"].append({
                "task_id": task_id,
                "status": task.status.value,
                "prompt": task.prompt[:50] + "..." if len(task.prompt) > 50 else task.prompt,
                "created_at": task.created_at,
                "completed_at": task.completed_at,
                "result_count": len(task.result_urls),
                "error": task.error_message
            })
        
        return summary


# 全局实例
async_image_manager = AsyncImageManager()
