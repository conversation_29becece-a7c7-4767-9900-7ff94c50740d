# Genify LangGraph 工作流图表

## 1. 研究型 Agent 工作流 (graph.py)

```mermaid
graph TB
    START1([开始]) --> generate_query[生成搜索查询]
    generate_query --> continue_to_web_research{分发搜索任务}
    continue_to_web_research --> web_research[网络搜索]
    web_research --> reflection[反思分析]
    reflection --> evaluate_research{评估研究结果}
    evaluate_research -->|信息充分或达到最大循环次数| finalize_answer[生成最终答案]
    evaluate_research -->|需要更多信息| web_research
    finalize_answer --> END1([结束])
```

**功能说明：**
- **generate_query**: 使用 Gemini 模型生成优化的搜索查询
- **web_research**: 并行执行多个网络搜索，调用 Google Search API
- **reflection**: 分析搜索结果，识别知识缺口，生成后续查询
- **evaluate_research**: 决定是否继续搜索或结束研究
- **finalize_answer**: 整合所有搜索结果，生成带引用的最终答案

## 2. 插画生成 Agent 工作流 (illustration_graph.py)

```mermaid
graph TB
    START2([开始]) --> input_handler[输入处理]
    input_handler --> story_splitter[故事分段]
    story_splitter --> character_extractor[角色提取]
    character_extractor --> should_generate_character{是否有主角或重要角色?}
    should_generate_character -->|是| character_image_generator[角色基准图生成]
    should_generate_character -->|否| storyboard_generator[分镜生成]
    character_image_generator --> storyboard_generator
    storyboard_generator --> scene_prompt_optimizer[场景提示词优化]
    scene_prompt_optimizer --> should_continue{是否有有效提示词?}
    should_continue -->|是| unified_image_generator[统一图像生成]
    should_continue -->|否| finalize_illustration[最终化插画]
    unified_image_generator --> should_merge{是否有多张成功图片?}
    should_merge -->|是| image_merger[图像合并]
    should_merge -->|否| finalize_illustration
    image_merger --> finalize_illustration
    finalize_illustration --> END2([结束])
```

**功能说明：**
- **input_handler**: 处理用户输入的故事内容
- **story_splitter**: 将故事分解为多个场景段落
- **character_extractor**: 识别并提取故事中的角色信息
- **character_image_generator**: 为主要角色生成基准图像
- **storyboard_generator**: 创建详细的分镜脚本
- **scene_prompt_optimizer**: 优化图像生成的提示词
- **unified_image_generator**: 统一生成所有场景图像
- **image_merger**: 将多张图片合并为最终插画
- **finalize_illustration**: 完成最终的插画处理

## 3. 整体系统架构流程

```mermaid
graph LR
    User[用户] --> Frontend[React 前端]
    Frontend --> API[FastAPI 后端]
    API --> Research[研究型 Agent]
    API --> Illustration[插画生成 Agent]
    Research --> Gemini[Gemini 模型]
    Research --> GoogleSearch[Google Search API]
    Illustration --> ImageAPI[图像生成 API]
    Gemini --> Results[研究结果]
    ImageAPI --> Images[生成的插画]
    Results --> Frontend
    Images --> Frontend
    Frontend --> User
```

**技术栈说明：**
- **前端**: React + Vite + Tailwind CSS + Shadcn UI
- **后端**: Python + LangGraph + FastAPI
- **AI模型**: Google Gemini 2.0/2.5 Flash
- **外部API**: Google Search API, 图像生成 API
- **部署**: Docker + Docker Compose