"""远程图像生成API客户端"""

import asyncio
import time
import os
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import httpx
import logging
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

logger = logging.getLogger(__name__)


class ModelType(Enum):
    """模型类型枚举"""
    ART = "art"  # 艺术风格
    REALISTIC = "realistic"  # 真实风格
    ANIME = "anime"  # 动漫风格


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"


@dataclass
class ImageGenerationTask:
    """图像生成任务"""
    task_id: str
    mark_id: Optional[str] = None
    status: TaskStatus = TaskStatus.PENDING
    prompt: str = ""
    model_type: ModelType = ModelType.ANIME
    result_urls: List[str] = None
    error_message: str = ""
    created_at: float = 0
    completed_at: Optional[float] = None
    
    def __post_init__(self):
        if self.result_urls is None:
            self.result_urls = []
        if self.created_at == 0:
            self.created_at = time.time()


class RemoteImageAPI:
    """远程图像生成API客户端"""
    
    def __init__(self, base_url: str = None, api_key: str = None):
        self.base_url = base_url or os.getenv("REMOTE_IMAGE_API_URL", "http://192.168.5.200")
        self.api_key = api_key or os.getenv("REMOTE_IMAGE_API_KEY", "ca5f728c93fef939e6f23baa34b53ed9aa2768dc")
        self.tasks: Dict[str, ImageGenerationTask] = {}
        self.polling_interval = int(os.getenv("POLLING_INTERVAL", "3"))  # 3秒轮询间隔
        self.max_polling_time = int(os.getenv("MAX_POLLING_TIME", "300"))  # 最大轮询时间5分钟
        
        # 模型配置
        self.model_configs = {
            ModelType.ART: {
                "model_id": "23887bba-507e-4249-a0e3-6951e4027f2b",
                "steps": 6,
                "cfg": 1,
                "sampler_name": "euler",
                "scheduler": "normal"
            },
            ModelType.REALISTIC: {
                "model_id": "34ec1b5a-8962-4a93-b047-68cec9691dc2",
                "steps": 25,
                "cfg": 4.5,
                "sampler_name": "dpmpp_2m_sde_gpu",
                "scheduler": "karras"
            },
            ModelType.ANIME: {
                "model_id": "cb4af9c7-41b0-47d3-944a-221446c7b8bc",
                "steps": 25,
                "cfg": 7,
                "sampler_name": "euler_ancestral",
                "scheduler": "karras"
            }
        }
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            "Content-Type": "application/json",
            "authorization": self.api_key,  # 直接使用API密钥，不加Bearer前缀
            "Platform": "Web"  # 添加Platform请求头
        }
    
    def _build_generation_payload(
        self, 
        prompt: str, 
        model_type: ModelType,
        width: int = 768,
        height: int = 1344,
        negative_prompt: str = ""
    ) -> Dict[str, Any]:
        """构建生图请求载荷"""
        config = self.model_configs[model_type]
        
        # 根据模型类型设置默认负面提示词
        if not negative_prompt:
            if model_type == ModelType.ANIME:
                negative_prompt = "(nsfw:0.7), (worst quality:1.5), (low quality:1.5), (normal quality:1.5), lowres,watermark, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, jpeg artifacts, signature, username, blurry, artist name"
            elif model_type == ModelType.REALISTIC:
                negative_prompt = "NSFW, watermark"
            else:
                negative_prompt = ""
        
        payload = {
            "highPixels": False,
            "model_id": config["model_id"],
            "prompt": prompt,
            "negative_prompt": negative_prompt,
            "resolution": {
                "width": width,
                "height": height,
                "batch_size": 1
            },
            "model_ability": {"anime_style_control": None} if model_type in [ModelType.ANIME, ModelType.REALISTIC] else {},
            "seed": int(time.time() * 1000) % 100000000000,  # 随机种子
            "steps": config["steps"],
            "cfg": config["cfg"],
            "sampler_name": config["sampler_name"],
            "scheduler": config["scheduler"],
            "ponyTags": {},
            "denoise": 1,
            "hires_fix_denoise": 0.5,
            "hires_scale": 2,
            "multi_img2img_info": {"style_list": []},
            "img_control_info": {"style_list": []},
            "continueCreate": False
        }
        
        return payload

    def _build_multi_edit_payload(
        self,
        prompt: str,
        image_urls: List[str],
        width: int = 2048,
        height: int = 2048,
        prompt_id: str = None
    ) -> Dict[str, Any]:
        """构建多图生图请求载荷"""
        # 使用flux模型进行多图生图
        payload = {
            "model_id": "flux3dc-cff2-4177-ad3a-28d9b4d3ff48",
            "promptId": prompt_id or f"00018655675954497479b6ada469852a",
            "prompt": prompt,
            "resolution": {
                "width": width,
                "height": height,
                "batch_size": 1
            },
            "multiImgEditPara": [{"imgUrl": url} for url in image_urls[:4]]  # 最大4张图片
        }

        return payload

    async def create_generation_task(
        self,
        task_id: str,
        prompt: str,
        model_type: ModelType = ModelType.ANIME,
        width: int = 768,
        height: int = 1344
    ) -> ImageGenerationTask:
        """创建图像生成任务"""
        task = ImageGenerationTask(
            task_id=task_id,
            prompt=prompt,
            model_type=model_type
        )
        
        try:
            payload = self._build_generation_payload(prompt, model_type, width, height)
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/api/gen/create",
                    json=payload,
                    headers=self._get_headers()
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("status") == 0:
                        task.mark_id = result["data"]["markId"]
                        task.status = TaskStatus.PROCESSING
                        logger.info(f"Task {task_id} created with markId: {task.mark_id}")
                    else:
                        task.status = TaskStatus.FAILED
                        task.error_message = result.get("message", "Unknown error")
                        logger.error(f"Task {task_id} creation failed: {task.error_message}")
                else:
                    task.status = TaskStatus.FAILED
                    task.error_message = f"HTTP {response.status_code}: {response.text}"
                    logger.error(f"Task {task_id} HTTP error: {task.error_message}")
                    
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            logger.error(f"Task {task_id} exception: {task.error_message}")
        
        self.tasks[task_id] = task
        return task

    async def create_multi_edit_task(
        self,
        task_id: str,
        prompt: str,
        image_urls: List[str],
        width: int = 2048,
        height: int = 2048,
        prompt_id: str = None
    ) -> ImageGenerationTask:
        """创建多图生图任务"""
        task = ImageGenerationTask(
            task_id=task_id,
            prompt=prompt,
            model_type=ModelType.ART  # 使用艺术模型
        )

        try:
            payload = self._build_multi_edit_payload(prompt, image_urls, width, height, prompt_id)

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/api/gen/multi-edit",
                    json=payload,
                    headers=self._get_headers()
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get("status") == 0:
                        task.mark_id = result["data"]["markId"]
                        task.status = TaskStatus.PROCESSING
                        logger.info(f"Multi-edit task {task_id} created with markId: {task.mark_id}")
                    else:
                        task.status = TaskStatus.FAILED
                        task.error_message = result.get("message", "Unknown error")
                        logger.error(f"Multi-edit task {task_id} creation failed: {task.error_message}")
                else:
                    task.status = TaskStatus.FAILED
                    task.error_message = f"HTTP {response.status_code}: {response.text}"
                    logger.error(f"Multi-edit task {task_id} HTTP error: {task.error_message}")

        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            logger.error(f"Multi-edit task {task_id} exception: {str(e)}")

        self.tasks[task_id] = task
        return task

    async def poll_task_results(self, mark_ids: List[str]) -> Dict[str, Dict[str, Any]]:
        """轮询任务结果"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/api/task/batch-process-task",
                    json=mark_ids,
                    headers=self._get_headers()
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("status") == 0:
                        # 将结果按markId组织
                        results = {}
                        for item in result.get("data", []):
                            mark_id = item.get("markId")
                            if mark_id:
                                results[mark_id] = item
                        return results
                    else:
                        logger.error(f"Polling failed: {result.get('message')}")
                        return {}
                else:
                    logger.error(f"Polling HTTP error: {response.status_code}")
                    return {}
                    
        except Exception as e:
            logger.error(f"Polling exception: {str(e)}")
            return {}
    
    async def wait_for_tasks_completion(
        self, 
        task_ids: List[str],
        progress_callback: Optional[callable] = None
    ) -> Dict[str, ImageGenerationTask]:
        """等待任务完成"""
        start_time = time.time()
        
        while True:
            # 检查超时
            if time.time() - start_time > self.max_polling_time:
                logger.warning("Polling timeout reached")
                break
            
            # 获取需要轮询的任务
            pending_tasks = [
                task for task_id, task in self.tasks.items()
                if task_id in task_ids and task.status == TaskStatus.PROCESSING and task.mark_id
            ]
            
            if not pending_tasks:
                break
            
            # 轮询结果
            mark_ids = [task.mark_id for task in pending_tasks]
            results = await self.poll_task_results(mark_ids)
            
            # 更新任务状态
            for task in pending_tasks:
                if task.mark_id in results:
                    result_data = results[task.mark_id]
                    if result_data.get("status") == "success":
                        task.status = TaskStatus.SUCCESS
                        task.completed_at = time.time()
                        # 提取图片URL
                        img_urls = result_data.get("img_urls", [])
                        task.result_urls = [img.get("imgUrl") for img in img_urls if img.get("imgUrl")]
                        logger.info(f"Task {task.task_id} completed successfully")
                    elif result_data.get("status") == "failed":
                        task.status = TaskStatus.FAILED
                        task.error_message = result_data.get("info", "Generation failed")
                        task.completed_at = time.time()
                        logger.error(f"Task {task.task_id} failed: {task.error_message}")
            
            # 调用进度回调
            if progress_callback:
                completed_count = sum(1 for task_id in task_ids if self.tasks[task_id].status in [TaskStatus.SUCCESS, TaskStatus.FAILED])
                progress_callback(completed_count, len(task_ids))
            
            # 检查是否所有任务都完成
            all_completed = all(
                self.tasks[task_id].status in [TaskStatus.SUCCESS, TaskStatus.FAILED]
                for task_id in task_ids
            )
            
            if all_completed:
                break
            
            # 等待下次轮询
            await asyncio.sleep(self.polling_interval)
        
        return {task_id: self.tasks[task_id] for task_id in task_ids}
    
    def get_task_status(self, task_id: str) -> Optional[ImageGenerationTask]:
        """获取任务状态"""
        return self.tasks.get(task_id)
    
    def get_all_tasks_status(self) -> Dict[str, ImageGenerationTask]:
        """获取所有任务状态"""
        return self.tasks.copy()


# 全局实例
remote_image_api = RemoteImageAPI()
