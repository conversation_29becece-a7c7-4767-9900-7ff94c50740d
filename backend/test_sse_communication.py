#!/usr/bin/env python3
"""
测试SSE通信和前后端数据传递
"""

import asyncio
import json
import sys
import uuid
sys.path.insert(0, 'src')

async def test_sse_workflow():
    """测试SSE工作流通信"""
    print("🧪 测试SSE工作流通信...")
    
    try:
        from agent.progress_manager import ProgressManager
        from agent.illustration_graph import generate_illustration_sync
        
        # 创建进度管理器
        progress_manager = ProgressManager()
        session_id = str(uuid.uuid4())
        
        print(f"📍 创建会话: {session_id}")
        progress_manager.start_session(session_id)
        
        # 创建进度回调函数
        def progress_callback(node_name: str, status: str, data=None):
            print(f"📊 进度更新: {node_name} -> {status}")
            progress_manager.update_progress(session_id, node_name, status, data)
        
        # 模拟工作流调用
        print("🔄 开始插画生成...")
        result = generate_illustration_sync(
            user_input="小猫在花园里玩耍",
            style_preference="anime",
            num_panels=2,
            progress_callback=progress_callback
        )
        
        print("✅ 工作流完成")
        print(f"📋 结果类型: {type(result)}")
        
        # 设置最终结果
        print("📤 设置最终结果...")
        progress_manager.set_final_result(session_id, result)
        
        # 测试SSE通信机制
        print("\n📡 测试SSE通信机制...")
        
        # 检查是否完成
        is_completed = progress_manager.is_completed(session_id)
        print(f"✅ 完成状态: {is_completed}")
        
        # 获取最终结果
        final_result = progress_manager.get_final_result(session_id)
        print(f"📦 最终结果获取: {final_result is not None}")
        
        if final_result:
            print(f"📊 最终结果结构: type={final_result.get('type')}, success={final_result.get('success')}")
            
            # 检查数据完整性
            data = final_result.get('data', {})
            if data:
                print(f"📋 数据字段检查:")
                print(f"  - processed_story: {'存在' if data.get('processed_story') else '缺失'}")
                print(f"  - characters: {len(data.get('characters', []))} 个")
                print(f"  - storyboards: {len(data.get('storyboards', []))} 个")
                print(f"  - generated_images: {len(data.get('generated_images', []))} 个")
                print(f"  - final_illustration: {'存在' if data.get('final_illustration') else '缺失'}")
            
            # 测试JSON序列化
            try:
                json_str = json.dumps(final_result, ensure_ascii=False)
                print("✅ JSON序列化成功")
                print(f"📏 JSON长度: {len(json_str)} 字符")
            except Exception as e:
                print(f"❌ JSON序列化失败: {e}")
                return False
        else:
            print("❌ 未获取到最终结果")
            return False
        
        # 模拟获取进度更新
        print("\n📊 测试进度更新...")
        progress_updates = progress_manager.get_progress_updates(session_id)
        print(f"📈 进度更新数量: {len(progress_updates)}")
        
        # 清理
        progress_manager.cleanup_session(session_id)
        print("🧹 会话清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_mock_sse_stream():
    """模拟SSE流测试"""
    print("\n🌊 模拟SSE流测试...")
    
    try:
        from agent.progress_manager import ProgressManager
        
        progress_manager = ProgressManager()
        session_id = str(uuid.uuid4())
        progress_manager.start_session(session_id)
        
        # 模拟逐步进度更新
        nodes = [
            'input_handler',
            'story_splitter', 
            'character_extractor',
            'storyboard_generator',
            'scene_prompt_optimizer',
            'unified_image_generator',
            'image_merger',
            'finalize_illustration'
        ]
        
        print("📡 模拟SSE流推送...")
        for i, node in enumerate(nodes):
            # 开始处理
            progress_manager.update_progress(session_id, node, "in_progress")
            
            # 模拟获取更新（模拟前端轮询）
            updates = progress_manager.get_progress_updates(session_id)
            for update in updates:
                print(f"📨 SSE推送: {update.get('title')} - {update.get('description')}")
            
            await asyncio.sleep(0.2)  # 模拟处理时间
            
            # 完成处理
            progress_manager.update_progress(session_id, node, "completed")
            
            # 模拟获取更新
            updates = progress_manager.get_progress_updates(session_id)
            for update in updates:
                print(f"📨 SSE推送: {update.get('title')} - {update.get('description')}")
            
            await asyncio.sleep(0.1)
        
        # 设置最终结果
        mock_result = {
            "final_illustration": "mock://merged_illustration.jpg",
            "processed_story": "小猫在花园里快乐地玩耍...",
            "characters": [{"name": "小猫", "role": "main"}],
            "storyboards": [{"panel_id": 1, "scene_description": "小猫在花园"}],
            "generated_images": [{"panel_id": 1, "success": True, "image_url": "mock://image1.jpg"}]
        }
        
        progress_manager.set_final_result(session_id, mock_result)
        
        # 模拟最终结果推送
        if progress_manager.is_completed(session_id):
            final_result = progress_manager.get_final_result(session_id)
            if final_result:
                print(f"📨 SSE最终推送: type={final_result.get('type')}, success={final_result.get('success')}")
                print("✅ 模拟SSE流测试完成")
                return True
        
        print("❌ 模拟SSE流测试失败")
        return False
        
    except Exception as e:
        print(f"❌ 模拟SSE流测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始SSE通信验证测试")
    print("=" * 50)
    
    # 测试完整工作流
    workflow_ok = await test_sse_workflow()
    
    # 测试模拟SSE流
    sse_ok = await test_mock_sse_stream()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"  - 完整工作流通信: {'✅ 通过' if workflow_ok else '❌ 失败'}")
    print(f"  - 模拟SSE流: {'✅ 通过' if sse_ok else '❌ 失败'}")
    
    if workflow_ok and sse_ok:
        print("\n🎉 SSE通信验证全部通过！")
        print("💡 建议：")
        print("  1. 重新启动后端服务器")
        print("  2. 使用前端测试新的生成流程")
        print("  3. 检查前端控制台是否收到最终结果")
    else:
        print("\n⚠️ 仍有问题需要解决")
    
    print("\n🔚 测试完成")

if __name__ == "__main__":
    asyncio.run(main())