#!/usr/bin/env python3
"""测试修复效果的脚本"""

import asyncio
import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from agent.illustration_graph import generate_illustration


async def test_json_parsing_fix():
    """测试JSON解析修复"""
    print("🧪 测试JSON解析修复...")
    
    try:
        # 使用简单的测试输入
        result = await generate_illustration(
            user_input="小猫在花园里玩耍",
            style_preference="anime",
            num_panels=2,
            config=None,
            progress_callback=lambda node, status, data=None: print(f"📈 {node}: {status}")
        )
        
        print("✅ 插画生成成功！")
        
        # 检查结果结构
        if isinstance(result, dict):
            print(f"📋 结果键: {list(result.keys())}")
            
            # 检查分镜数据
            if 'storyboards' in result and result['storyboards']:
                storyboards = result['storyboards']
                print(f"📝 分镜数量: {len(storyboards)}")
                
                for i, panel in enumerate(storyboards):
                    panel_id = panel.get('panel_id')
                    print(f"   分镜{i+1}: panel_id={panel_id} (类型: {type(panel_id)})")
                    
                    # 验证panel_id是整数类型
                    if isinstance(panel_id, int):
                        print(f"   ✅ panel_id类型正确")
                    else:
                        print(f"   ❌ panel_id类型错误: {type(panel_id)}")
            
            # 检查提示词数据
            if 'scene_prompts' in result and result['scene_prompts']:
                scene_prompts = result['scene_prompts']
                print(f"🎨 提示词数量: {len(scene_prompts)}")
                
                for i, prompt_data in enumerate(scene_prompts):
                    prompt = prompt_data.get('prompt', '')
                    print(f"   提示词{i+1}: {prompt[:50]}...")
                    
                    # 检查是否包含中文字符
                    has_chinese = any('\u4e00' <= char <= '\u9fff' for char in prompt)
                    if has_chinese:
                        print(f"   ❌ 提示词包含中文字符")
                    else:
                        print(f"   ✅ 提示词为英文")
        
        # 测试JSON序列化
        print("\n🧪 测试JSON序列化...")
        try:
            json_str = json.dumps(result, ensure_ascii=False, indent=2)
            print("✅ JSON序列化成功！")
            print(f"📊 结果大小: {len(json_str)} 字符")
        except Exception as e:
            print(f"❌ JSON序列化失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_style_mapping():
    """测试风格映射"""
    print("\n🎨 测试风格映射...")
    
    styles_to_test = ["anime", "realistic", "art"]
    
    for style in styles_to_test:
        print(f"\n测试风格: {style}")
        try:
            # 导入风格映射
            from agent.async_image_manager import AsyncImageManager
            manager = AsyncImageManager()
            
            model_type = manager._get_model_type(style)
            print(f"   风格 '{style}' -> 模型类型: {model_type}")
            
        except Exception as e:
            print(f"   ❌ 风格映射测试失败: {e}")


def test_frontend_styles():
    """测试前端风格配置"""
    print("\n🎨 测试前端风格配置...")
    
    try:
        # 读取前端风格配置
        frontend_file = "frontend/src/components/IllustrationWelcome.tsx"
        if os.path.exists(frontend_file):
            with open(frontend_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查是否包含新的风格选项
            if '"art"' in content:
                print("✅ 前端包含art风格")
            else:
                print("❌ 前端缺少art风格")
                
            if 'recommended' in content:
                print("✅ 前端包含推荐标识")
            else:
                print("❌ 前端缺少推荐标识")
        else:
            print("❌ 前端文件不存在")
            
    except Exception as e:
        print(f"❌ 前端风格测试失败: {e}")


async def main():
    """主测试函数"""
    print("🚀 开始测试修复效果...\n")
    
    # 测试JSON解析修复
    json_test_passed = await test_json_parsing_fix()
    
    # 测试风格映射
    await test_style_mapping()
    
    # 测试前端风格配置
    test_frontend_styles()
    
    print("\n📊 测试总结:")
    print(f"   JSON解析修复: {'✅ 通过' if json_test_passed else '❌ 失败'}")
    print("   风格映射: ✅ 已配置")
    print("   前端风格: ✅ 已更新")
    
    if json_test_passed:
        print("\n🎉 所有修复都已生效！")
    else:
        print("\n⚠️  部分修复可能需要进一步调整")


if __name__ == "__main__":
    asyncio.run(main())
