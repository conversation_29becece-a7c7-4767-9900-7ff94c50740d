#!/usr/bin/env python3
"""
端到端测试脚本 - 验证前后端整体功能

测试前端是否能正确显示后端推送的进度信息
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime


async def test_e2e_illustration_generation():
    """端到端测试插画生成功能"""
    print("🚀 开始端到端测试")
    
    # 测试数据
    test_data = {
        "user_input": "一个勇敢的小女孩与朋友们踏上寻找魔法水晶的冒险之旅",
        "style_preference": "anime", 
        "num_panels": 4
    }
    
    print(f"📝 测试数据: {test_data}")
    
    async with aiohttp.ClientSession() as session:
        print(f"⏰ 开始时间: {datetime.now().strftime('%H:%M:%S')}")
        
        try:
            # 步骤1: 启动生成
            print(f"\n📤 发送启动请求...")
            start_time = time.time()
            
            async with session.post(
                "http://localhost:8080/api/start-generation",
                json=test_data,
                headers={"Platform": "Web"}
            ) as response:
                response_time = time.time() - start_time
                print(f"📬 启动响应时间: {response_time:.2f}秒")
                
                if response.status == 200:
                    result = await response.json()
                    session_id = result.get("session_id")
                    print(f"✅ 启动成功: {result}")
                    
                    if session_id:
                        # 步骤2: 监听完整进度流
                        await monitor_complete_progress(session, session_id)
                    else:
                        print("❌ 未获得会话ID")
                else:
                    text = await response.text()
                    print(f"❌ 启动失败: {response.status} - {text}")
                    
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")


async def monitor_complete_progress(session, session_id):
    """监听完整的进度流"""
    print(f"\n📡 开始监听进度流: {session_id}")
    
    progress_stats = {
        'total_updates': 0,
        'main_stages': 0,
        'image_progress': 0,
        'start_time': time.time()
    }
    
    try:
        async with session.get(
            f"http://localhost:8080/api/progress/{session_id}",
            headers={"Platform": "Web"}
        ) as response:
            
            if response.status != 200:
                print(f"❌ 进度流连接失败: {response.status}")
                return
            
            print(f"📊 进度流已连接")
            
            async for line in response.content:
                if line.startswith(b'data: '):
                    progress_stats['total_updates'] += 1
                    data_str = line[6:].decode('utf-8').strip()
                    
                    try:
                        data = json.loads(data_str)
                        
                        # 分类统计
                        if data.get("type") == "progress":
                            if data.get("node_name") == "image_generation_progress":
                                progress_stats['image_progress'] += 1
                                print(f"🎨 图像进度: {data.get('description')}")
                            else:
                                progress_stats['main_stages'] += 1
                                stage_name = data.get('node_name', 'unknown')
                                status = data.get('status', 'unknown')
                                title = data.get('title', 'unknown')
                                description = data.get('description', 'unknown')
                                
                                status_icon = "🔄" if status == "in_progress" else "✅" if status == "completed" else "❓"
                                print(f"{status_icon} {title} - {description}")
                        
                        elif data.get("type") == "result":
                            print(f"🏁 收到最终结果!")
                            result_data = data.get('data', {})
                            print(f"   - 最终插画: {result_data.get('final_illustration')}")
                            print(f"   - 处理后故事: {result_data.get('processed_story', '')[:50]}...")
                            print(f"   - 角色数量: {len(result_data.get('characters', []))}")
                            print(f"   - 分镜数量: {len(result_data.get('storyboards', []))}")
                            print(f"   - 生成图片: {len(result_data.get('generated_images', []))}")
                            break
                            
                        elif data.get("type") == "error":
                            print(f"❌ 收到错误: {data.get('message')}")
                            break
                            
                    except json.JSONDecodeError:
                        print(f"⚠️  JSON解析失败: {data_str[:100]}...")
                
                # 防止过长等待
                if time.time() - progress_stats['start_time'] > 120:
                    print(f"⏰ 超时停止监听")
                    break
    
        # 统计报告
        total_time = time.time() - progress_stats['start_time']
        print(f"\n📈 测试统计:")
        print(f"   - 总耗时: {total_time:.2f}秒")
        print(f"   - 总进度更新: {progress_stats['total_updates']}")
        print(f"   - 主要阶段: {progress_stats['main_stages']}")
        print(f"   - 图像进度更新: {progress_stats['image_progress']}")
        
        # 验证结果
        if progress_stats['total_updates'] > 20 and progress_stats['image_progress'] > 5:
            print(f"✅ 测试通过：进度更新充足，图像进度正常")
        else:
            print(f"⚠️  测试提醒：进度更新可能不够充分")
            
    except Exception as e:
        print(f"❌ 进度监听异常: {str(e)}")


async def main():
    print("=" * 60)
    print("🧪 端到端测试 - 插画生成与进度显示")
    print("=" * 60)
    
    # 检查后端服务器
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8080/api/health") as response:
                if response.status == 200:
                    health_data = await response.json()
                    print(f"✅ 后端服务器健康: {health_data}")
                else:
                    print(f"❌ 后端服务器异常: {response.status}")
                    return
    except Exception as e:
        print(f"❌ 无法连接后端服务器: {str(e)}")
        return
    
    # 检查前端服务器
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:5175/app/") as response:
                if response.status == 200:
                    print(f"✅ 前端服务器可访问: http://localhost:5175/app/")
                else:
                    print(f"⚠️  前端服务器状态: {response.status}")
    except Exception as e:
        print(f"⚠️  前端服务器访问异常: {str(e)}")
        print(f"提示: 请确保前端服务器运行在 http://localhost:5175")
    
    print()
    # 执行主测试
    await test_e2e_illustration_generation()
    
    print("\n" + "=" * 60)
    print("🏁 端到端测试完成")
    print("=" * 60)
    print("💡 测试建议:")
    print("   1. 在浏览器中访问 http://localhost:5175/app/")
    print("   2. 输入相同的测试故事并提交")
    print("   3. 观察前端进度显示是否与后端推送一致")


if __name__ == "__main__":
    asyncio.run(main())