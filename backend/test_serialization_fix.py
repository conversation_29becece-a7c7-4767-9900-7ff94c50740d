#!/usr/bin/env python3
"""
测试序列化修复
"""
import asyncio
import sys
import os
import json

# 添加src目录到路径
sys.path.append('src')

async def test_serialization_fix():
    """测试序列化修复"""
    try:
        from agent.illustration_graph import generate_illustration
        
        print('🔄 测试序列化修复...')
        
        # 收集进度更新
        progress_updates = []
        
        def progress_callback(node_name, status, data=None):
            update = f'{node_name} -> {status}'
            print(f'📊 进度: {update}')
            progress_updates.append((node_name, status))
        
        # 执行工作流（使用简短输入）
        try:
            result = await asyncio.wait_for(
                generate_illustration(
                    user_input='小猫',
                    style_preference='anime',
                    num_panels=1,
                    progress_callback=progress_callback
                ),
                timeout=60  # 1分钟超时
            )
        except asyncio.TimeoutError:
            print('⏰ 工作流超时，但我们可以检查序列化')
            result = {"test": "timeout"}
        
        print(f'\n📈 收到 {len(progress_updates)} 个进度更新')
        
        # 测试序列化
        print('\n🧪 测试JSON序列化...')
        try:
            json_str = json.dumps(result, ensure_ascii=False, indent=2)
            print('✅ JSON序列化成功！')
            print(f'📊 结果大小: {len(json_str)} 字符')
            
            # 显示结果的键
            if isinstance(result, dict):
                print(f'📋 结果键: {list(result.keys())}')
                
                # 检查是否还有progress_callback
                if 'progress_callback' in result:
                    print('❌ 结果中仍包含progress_callback')
                else:
                    print('✅ 结果中已移除progress_callback')
            
        except Exception as e:
            print(f'❌ JSON序列化失败: {e}')
            
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_serialization_fix())
