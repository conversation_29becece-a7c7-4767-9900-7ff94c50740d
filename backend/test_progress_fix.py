#!/usr/bin/env python3
"""
测试进度回调修复效果
验证所有节点是否正确调用进度回调
"""

import asyncio
import json
import sys
import uuid
import time
import os

# 添加正确的路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

async def test_progress_callbacks():
    """测试所有节点的进度回调"""
    print("🧪 测试进度回调修复效果...")
    
    try:
        from agent.progress_manager import ProgressManager
        from agent.illustration_graph import generate_illustration_sync
        
        # 创建进度管理器
        progress_manager = ProgressManager()
        session_id = str(uuid.uuid4())
        
        print(f"📍 创建会话: {session_id}")
        progress_manager.start_session(session_id)
        
        # 收集所有进度更新
        received_updates = []
        
        # 创建进度回调函数
        def progress_callback(node_name: str, status: str, data=None):
            update_info = f"{node_name} -> {status}"
            print(f"📊 进度更新: {update_info}")
            received_updates.append((node_name, status, time.time()))
            progress_manager.update_progress(session_id, node_name, status, data)
        
        # 执行工作流
        print("🔄 开始插画生成...")
        start_time = time.time()
        
        result = generate_illustration_sync(
            user_input="小猫在花园里追逐蝴蝶",
            style_preference="anime",
            num_panels=2,
            progress_callback=progress_callback
        )
        
        end_time = time.time()
        print(f"✅ 工作流完成，耗时: {end_time - start_time:.2f}秒")
        
        # 分析收集到的进度更新
        print("\n📈 进度更新分析:")
        print(f"总共收到 {len(received_updates)} 个进度更新")
        
        # 统计各节点的进度更新
        node_updates = {}
        for node_name, status, timestamp in received_updates:
            if node_name not in node_updates:
                node_updates[node_name] = []
            node_updates[node_name].append(status)
        
        print("\n📋 各节点进度统计:")
        expected_nodes = [
            'input_handler',
            'story_splitter', 
            'character_extractor',
            'storyboard_generator',
            'scene_prompt_optimizer',
            'unified_image_generator',
            'image_merger',
            'finalize_illustration'
        ]
        
        for node in expected_nodes:
            if node in node_updates:
                statuses = node_updates[node]
                has_in_progress = 'in_progress' in statuses
                has_completed = 'completed' in statuses
                status_icon = "✅" if has_in_progress and has_completed else "⚠️"
                print(f"  {status_icon} {node}: {' -> '.join(statuses)}")
            else:
                print(f"  ❌ {node}: 未收到进度更新")
        
        # 设置最终结果并测试SSE
        print("\n📤 设置最终结果...")
        progress_manager.set_final_result(session_id, result)
        
        # 测试进度获取
        print("\n📡 测试进度获取机制...")
        all_progress = progress_manager.get_all_progress_updates(session_id)
        print(f"📊 历史进度数量: {len(all_progress)}")
        
        # 模拟SSE获取
        pending_updates = progress_manager.get_progress_updates(session_id)
        print(f"📨 待推送更新: {len(pending_updates)}")
        
        # 检查完成状态
        is_completed = progress_manager.is_completed(session_id)
        print(f"✅ 完成状态: {is_completed}")
        
        if is_completed:
            final_result = progress_manager.get_final_result(session_id)
            if final_result:
                print(f"📦 最终结果: 类型={final_result['type']}, 成功={final_result['success']}")
        
        # 清理
        progress_manager.cleanup_session(session_id)
        
        return len(received_updates) > 0 and len(node_updates) >= 6  # 至少6个节点有进度更新
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_sse_integration():
    """测试SSE集成"""
    print("\n🌐 测试SSE集成...")
    
    try:
        from agent.progress_manager import ProgressManager
        
        progress_manager = ProgressManager()
        session_id = str(uuid.uuid4())
        progress_manager.start_session(session_id)
        
        # 模拟逐步进度更新
        nodes = [
            'input_handler',
            'story_splitter', 
            'character_extractor',
            'storyboard_generator',
            'scene_prompt_optimizer',
            'unified_image_generator',
            'image_merger',
            'finalize_illustration'
        ]
        
        print("📡 模拟SSE进度推送...")
        for node in nodes:
            # 开始处理
            progress_manager.update_progress(session_id, node, "in_progress")
            
            # 模拟SSE获取（获取历史进度）
            all_updates = progress_manager.get_all_progress_updates(session_id)
            print(f"📊 历史进度: {len(all_updates)} 个更新")
            
            # 模拟处理时间
            await asyncio.sleep(0.1)
            
            # 完成处理
            progress_manager.update_progress(session_id, node, "completed")
            
            # 模拟SSE获取新进度
            pending_updates = progress_manager.get_progress_updates(session_id)
            for update in pending_updates:
                print(f"📨 SSE推送: {update['title']} - {update['description']}")
        
        # 设置最终结果
        mock_result = {
            "final_illustration": "mock://merged_illustration.jpg",
            "processed_story": "测试故事内容...",
            "characters": [{"name": "测试角色", "role": "main"}],
            "storyboards": [{"panel_id": 1, "scene_description": "测试分镜"}],
            "generated_images": [{"panel_id": 1, "success": True, "image_url": "mock://image1.jpg"}]
        }
        
        progress_manager.set_final_result(session_id, mock_result)
        
        # 验证最终结果
        if progress_manager.is_completed(session_id):
            final_result = progress_manager.get_final_result(session_id)
            if final_result:
                print(f"📨 SSE最终推送: {final_result['type']}, 成功: {final_result['success']}")
                return True
        
        return False
        
    except Exception as e:
        print(f"❌ SSE测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始进度回调修复验证测试")
    print("=" * 60)
    
    # 测试进度回调
    progress_ok = await test_progress_callbacks()
    
    # 测试SSE集成
    sse_ok = await test_sse_integration()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"  - 进度回调功能: {'✅ 正常' if progress_ok else '❌ 异常'}")
    print(f"  - SSE集成功能: {'✅ 正常' if sse_ok else '❌ 异常'}")
    
    if progress_ok and sse_ok:
        print("\n🎉 进度回调修复验证通过！")
        print("\n💡 修复内容：")
        print("  1. 为image_merger节点添加了进度回调")
        print("  2. 改进了进度管理器的历史进度获取功能")
        print("  3. 增强了SSE端点的进度推送机制")
        print("  4. 添加了前端进度接收的日志调试")
        print("\n🔧 下一步操作：")
        print("  1. 重启后端服务器: python run_server.py")
        print("  2. 清除浏览器缓存并刷新前端")
        print("  3. 测试插画生成功能")
    else:
        print("\n⚠️ 仍有问题需要进一步调试")
    
    print("\n🔚 测试完成")

if __name__ == "__main__":
    asyncio.run(main())