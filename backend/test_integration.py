#!/usr/bin/env python3
"""
完整的前后端集成测试

验证真实工作流、进度推送和前端显示的完整流程
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime


async def test_complete_integration():
    """完整的集成测试"""
    print("🧪 完整前后端集成测试")
    
    # 使用更简单的故事以提高成功率
    test_data = {
        "user_input": "小兔子在花园里玩耍",
        "style_preference": "anime", 
        "num_panels": 2
    }
    
    print(f"📝 测试数据: {test_data}")
    
    async with aiohttp.ClientSession() as session:
        print(f"⏰ 开始时间: {datetime.now().strftime('%H:%M:%S')}")
        
        try:
            # 步骤1: 启动生成
            print(f"\n📤 发送启动请求...")
            start_time = time.time()
            
            async with session.post(
                "http://localhost:8080/api/start-generation",
                json=test_data,
                headers={"Platform": "Web"}
            ) as response:
                response_time = time.time() - start_time
                print(f"📬 启动响应时间: {response_time:.2f}秒")
                
                if response.status == 200:
                    result = await response.json()
                    session_id = result.get("session_id")
                    print(f"✅ 启动成功，会话ID: {session_id}")
                    
                    if session_id:
                        # 步骤2: 详细监听进度
                        await monitor_detailed_progress(session, session_id)
                    else:
                        print("❌ 未获得会话ID")
                else:
                    text = await response.text()
                    print(f"❌ 启动失败: {response.status} - {text}")
                    
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")


async def monitor_detailed_progress(session, session_id):
    """详细监听进度"""
    print(f"\n📡 开始详细监听进度: {session_id}")
    
    progress_details = {
        'total_updates': 0,
        'start_time': time.time(),
        'stages': {},
        'image_progress': [],
        'workflow_type': 'unknown',
        'has_real_data': False
    }
    
    try:
        async with session.get(
            f"http://localhost:8080/api/progress/{session_id}",
            headers={"Platform": "Web"}
        ) as response:
            
            if response.status != 200:
                print(f"❌ 进度流连接失败: {response.status}")
                return
            
            print(f"📊 进度流已连接")
            print(f"⏳ 等待进度数据...")
            
            async for line in response.content:
                if line.startswith(b'data: '):
                    progress_details['total_updates'] += 1
                    data_str = line[6:].decode('utf-8').strip()
                    
                    try:
                        data = json.loads(data_str)
                        current_time = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        
                        if data.get("type") == "progress":
                            node_name = data.get('node_name', 'unknown')
                            status = data.get('status', 'unknown')
                            title = data.get('title', 'unknown')
                            description = data.get('description', 'unknown')
                            
                            # 记录阶段信息
                            if node_name not in progress_details['stages']:
                                progress_details['stages'][node_name] = {
                                    'title': title,
                                    'start_time': current_time,
                                    'status': status
                                }
                            else:
                                progress_details['stages'][node_name]['status'] = status
                            
                            # 检测工作流类型
                            if node_name == "image_generation_progress":
                                progress_details['image_progress'].append(description)
                                print(f"🎨 [{current_time}] 图像进度: {description}")
                            else:
                                status_icon = "🔄" if status == "in_progress" else "✅" if status == "completed" else "❓"
                                print(f"{status_icon} [{current_time}] {title} - {description}")
                                
                                # 检测是否有真实数据处理痕迹
                                if any(keyword in description.lower() for keyword in ['llm', '模型', '调用', '解析', '验证']):
                                    progress_details['has_real_data'] = True
                        
                        elif data.get("type") == "result":
                            print(f"🏁 [{current_time}] 收到最终结果!")
                            
                            result_data = data.get('data', {})
                            final_illustration = result_data.get('final_illustration', '')
                            processed_story = result_data.get('processed_story', '')
                            characters = result_data.get('characters', [])
                            storyboards = result_data.get('storyboards', [])
                            generated_images = result_data.get('generated_images', [])
                            
                            # 详细分析结果
                            print(f"\n📋 结果详细分析:")
                            
                            # 判断工作流类型
                            if 'mock://' in final_illustration:
                                progress_details['workflow_type'] = 'mock'
                                print(f"   🔄 工作流类型: Failover模式（模拟）")
                            elif 'http' in final_illustration:
                                progress_details['workflow_type'] = 'real'
                                print(f"   🌐 工作流类型: 真实工作流（网络图像）")
                            else:
                                progress_details['workflow_type'] = 'unknown'
                                print(f"   ❓ 工作流类型: 未知")
                            
                            print(f"   - 最终插画: {final_illustration}")
                            print(f"   - 故事长度: {len(processed_story)}字符")
                            print(f"   - 角色数量: {len(characters)}")
                            print(f"   - 分镜数量: {len(storyboards)}")
                            print(f"   - 生成图片: {len(generated_images)}")
                            
                            # 检查角色复杂度
                            if characters:
                                first_char = characters[0]
                                char_desc_len = len(first_char.get('description', ''))
                                char_appearance_len = len(first_char.get('appearance', ''))
                                print(f"   - 首个角色描述长度: {char_desc_len}字符")
                                print(f"   - 首个角色外貌长度: {char_appearance_len}字符")
                                
                                if char_desc_len > 50 or char_appearance_len > 50:
                                    progress_details['has_real_data'] = True
                                    print(f"   ✅ 检测到复杂角色数据，可能来自真实LLM")
                            
                            # 检查分镜复杂度
                            if storyboards:
                                first_storyboard = storyboards[0]
                                scene_desc_len = len(first_storyboard.get('scene_description', ''))
                                print(f"   - 首个分镜描述长度: {scene_desc_len}字符")
                                
                                if scene_desc_len > 50:
                                    progress_details['has_real_data'] = True
                                    print(f"   ✅ 检测到复杂分镜数据，可能来自真实LLM")
                            
                            break
                            
                        elif data.get("type") == "error":
                            print(f"❌ [{current_time}] 收到错误: {data.get('message')}")
                            break
                            
                    except json.JSONDecodeError:
                        print(f"⚠️  JSON解析失败: {data_str[:100]}...")
                
                # 防止无限等待
                if time.time() - progress_details['start_time'] > 600:  # 10分钟超时
                    print(f"⏰ 超时停止监听（10分钟）")
                    break
    
        # 最终分析
        total_time = time.time() - progress_details['start_time']
        
        print(f"\n📈 完整集成测试分析:")
        print(f"   - 总耗时: {total_time:.2f}秒")
        print(f"   - 总进度更新: {progress_details['total_updates']}")
        print(f"   - 执行阶段数: {len(progress_details['stages'])}")
        print(f"   - 图像进度更新: {len(progress_details['image_progress'])}")
        print(f"   - 工作流类型: {progress_details['workflow_type']}")
        print(f"   - 检测到真实数据: {'✅' if progress_details['has_real_data'] else '❌'}")
        
        print(f"\n📊 执行阶段详情:")
        for stage, info in progress_details['stages'].items():
            print(f"   - {info['title']}: {info['status']}")
        
        # 最终评估
        if progress_details['workflow_type'] == 'real' and progress_details['has_real_data']:
            print(f"\n🎉 测试成功：真实工作流正常执行，生成真实图像！")
        elif progress_details['workflow_type'] == 'mock':
            print(f"\n🔄 测试结果：触发Failover模式，使用模拟数据")
        else:
            print(f"\n❓ 测试结果：无法确定工作流执行类型")
            
    except Exception as e:
        print(f"❌ 进度监听异常: {str(e)}")


async def main():
    print("=" * 60)
    print("🧪 完整前后端集成测试")
    print("=" * 60)
    
    # 检查服务器
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8080/api/health") as response:
                if response.status == 200:
                    health_data = await response.json()
                    print(f"✅ 后端服务器健康: {health_data}")
                else:
                    print(f"❌ 后端服务器异常: {response.status}")
                    return
    except Exception as e:
        print(f"❌ 无法连接后端服务器: {str(e)}")
        return
    
    print()
    # 执行完整测试
    await test_complete_integration()
    
    print("\n" + "=" * 60)
    print("🏁 完整集成测试完成")
    print("=" * 60)
    print("💡 测试说明:")
    print("   - 如果看到网络图像URL，说明真实工作流成功")
    print("   - 如果看到mock://，说明触发了Failover机制")
    print("   - 复杂的角色和分镜描述表明LLM正常工作")


if __name__ == "__main__":
    asyncio.run(main())