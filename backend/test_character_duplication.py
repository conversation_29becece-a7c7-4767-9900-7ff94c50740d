#!/usr/bin/env python3
"""
测试角色信息重复问题
"""
import asyncio
import sys
import os
import time

# 添加src目录到路径
sys.path.append('src')

async def test_character_duplication():
    """测试角色信息重复问题"""
    try:
        from agent.illustration_graph import generate_illustration
        
        print('🔄 测试角色信息重复问题...')
        
        # 收集进度更新
        progress_updates = []
        
        def progress_callback(node_name, status, data=None):
            update = f'{node_name} -> {status}'
            print(f'📊 进度: {update}')
            progress_updates.append((node_name, status))
            
            # 如果有角色数据，打印出来
            if data and 'characters_count' in data:
                print(f'   角色数量: {data["characters_count"]}')
        
        # 执行工作流（使用较短的故事避免超时）
        start_time = time.time()
        
        try:
            result = await asyncio.wait_for(
                generate_illustration(
                    user_input='小猫玩耍',
                    style_preference='anime',
                    num_panels=1,
                    progress_callback=progress_callback
                ),
                timeout=120  # 2分钟超时
            )
        except asyncio.TimeoutError:
            print('⏰ 工作流超时，但我们可以检查已收集的信息')
            result = None
        
        end_time = time.time()
        
        print(f'\n📈 总共收到 {len(progress_updates)} 个进度更新')
        print(f'⏱️ 耗时: {end_time - start_time:.2f}秒')
        
        # 检查结果中的角色信息
        if result and 'characters' in result:
            characters = result['characters']
            print(f'\n👥 角色信息分析:')
            print(f'   总角色数量: {len(characters)}')
            
            # 检查是否有重复角色
            character_names = [char.get('name', '未知') for char in characters]
            unique_names = set(character_names)
            
            print(f'   唯一角色数量: {len(unique_names)}')
            print(f'   角色名称: {character_names}')
            
            if len(character_names) != len(unique_names):
                print('   ❌ 发现重复角色！')
                # 统计重复次数
                from collections import Counter
                name_counts = Counter(character_names)
                for name, count in name_counts.items():
                    if count > 1:
                        print(f'      "{name}" 重复了 {count} 次')
            else:
                print('   ✅ 没有重复角色')
        else:
            print('\n👥 没有角色信息可供分析')
            
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_character_duplication())
