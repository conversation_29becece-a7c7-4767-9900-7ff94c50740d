# LangGraph 插画生成 Agent 需求文档

## 一、项目目标
基于 LangGraph 构建一个智能 Agent，能够根据用户提示词自动生成具有故事连续性的插画。支持故事扩写、分镜拆解、角色一致性建模，并最终调用第三方图像生成 API（文生图 / 图生图）完成绘制。

## 二、功能需求

### 1. 输入处理
- 用户输入提示词（可能是简单描述，也可能是完整故事）。
- Agent 根据输入自动选择：
  - 摘要/总结。
  - 扩写/细化故事。

### 2. 故事分段与分镜
- 将处理后的故事拆分为多个分段（如 4 格漫画分镜）。
- 分镜需优化为：
  - 场景描述（环境、动作）。
  - 角色特征（外貌、服饰、风格）。
  - 道具与氛围。

### 3. 角色风格提取
- 提取主角及关键角色的：
  - 外貌特征。
  - 风格设定（Q版、写实、赛博朋克等）。
- 生成角色基准图（用于后续图生图保持一致性）。

### 4. 分镜提示词转换
- 将分镜优化为第三人称客观描述。  
- 生成用于 **文生图 / 图生图** 的提示词：  
  - 图生图：引用角色基准图，保持一致性。  
  - 文生图：直接用场景描述生成画面。

### 5. 图像生成
- 调用第三方 API（如 SDXL、Flux、MJ、或本地部署服务）。
- 执行以下流程：
  - 生成人物基准图。
  - 根据分镜调用文生图/图生图生成插画。
  - 输出每个分镜的图片。

### 6. 插画合并
- 将多张分镜图合成为：
  - 单独的插画序列。  
  - 或一张拼接图（4 格漫画）。

## 三、技术选型

- **LangGraph**：核心任务流转编排。
- **大语言模型（LLM）**：Qwen（替换默认 Gemini）。
- **代码框架**：基于 [Gemini Fullstack LangGraph Quickstart](https://github.com/google-gemini/gemini-fullstack-langgraph-quickstart) 修改。
- **图像生成工具**：
  - 支持文生图（Text-to-Image）。
  - 支持图生图（Image-to-Image，用于角色一致性）。

## 四、Agent 工作流

```mermaid
flowchart TD
    A[用户提示词输入] --> B[故事扩写 / 总结]
    B --> C[分段拆解]
    C --> D[提取角色特征]
    D --> E[生成角色基准图(图生图)]
    C --> F[分镜优化为第三人称提示词]
    F --> G[调用文生图/图生图 API]
    E --> G
    G --> H[生成分镜插画]
    H --> I[合并输出插画序列 / 拼接图]
```

## 五、实施清单

1. **环境搭建**
   - Fork `gemini-fullstack-langgraph-quickstart` 项目。
   - 替换默认 LLM 为 Qwen。

2. **节点实现**
   - `input_handler`：输入处理（总结/扩写）。
   - `story_splitter`：故事分段 → 分镜。
   - `character_extractor`：角色信息提取。  
   - `character_image_gen`：角色基准图生成（图生图）。
   - `scene_prompt_optimizer`：分镜转第三人称提示词。  
   - `image_generator`：调用文生图 / 图生图 API。  
   - `image_merger`：拼接输出结果。

3. **LangGraph 编排**
   - 按照工作流顺序构建有向图。
   - 增加条件分支（如用户输入过于简单则扩写，否则直接分镜）。

4. **前后端集成**
   - API 层：提供插画生成功能接口。  
   - 前端：输入提示词 → 获取生成插画结果。

5. **测试与优化**
   - 测试不同风格输入（Q版、写实）。
   - 验证角色一致性。  
   - 优化提示词生成效果。

## 六、未来扩展

- 增加多角色关系建模。  
- 增加对话式剧情分镜生成。  
- 接入缓存机制，避免重复生成相同角色基准图。  
- 提供多风格版本选择。  
