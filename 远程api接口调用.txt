有一个post art模型生图接口，偏艺术风格http://192.168.5.200/api/gen/create，application/json，authorization:ca5f728c93fef939e6f23baa34b53ed9aa2768dc，Platform:Web,请求为：
{"highPixels":false,"model_id":"23887bba-507e-4249-a0e3-6951e4027f2b","prompt":"a girl","negative_prompt":"","resolution":{"width":768,"height":1344,"batch_size":1},"model_ability":{},"seed":98175180350,"steps":6,"cfg":1,"sampler_name":"euler","scheduler":"normal","ponyTags":{},"denoise":1,"hires_fix_denoise":0.5,"hires_scale":2,"multi_img2img_info":{"style_list":[]},"img_control_info":{"style_list":[]},"continueCreate":false}
返回结构为：{
    "status": 0,
    "message": "success",
    "data": {
        "markId": "d6194183-ac9d-4cb7-ba04-32f73c764e6f",
        "featureName": "ttp",
        "fastHour": true,
        "index": -1
    }
}

有一个post 真实模型生图接口，偏真实风格http://192.168.5.200/api/gen/create，application/json，authorization:ca5f728c93fef939e6f23baa34b53ed9aa2768dc, Platform:Web，请求为：
{"highPixels":false,"model_id":"34ec1b5a-8962-4a93-b047-68cec9691dc2","prompt":"girl","negative_prompt":"NSFW, watermark","resolution":{"width":1024,"height":1024,"batch_size":1},"model_ability":{"anime_style_control":null},"seed":11187933947,"steps":25,"cfg":4.5,"sampler_name":"dpmpp_2m_sde_gpu","scheduler":"karras","ponyTags":{},"denoise":1,"hires_fix_denoise":0.5,"hires_scale":2,"multi_img2img_info":{"style_list":[]},"img_control_info":{"style_list":[]},"continueCreate":false}
返回结构为：{
    "status": 0,
    "message": "success",
    "data": {
        "markId": "d6194183-ac9d-4cb7-ba04-32f73c764e6f",
        "featureName": "ttp",
        "fastHour": true,
        "index": -1
    }
}

有一个post 动漫模型生图接口，偏动漫风格http://192.168.5.200/api/gen/create，application/json，authorization:ca5f728c93fef939e6f23baa34b53ed9aa2768dc, Platform:Web，请求为：
{"highPixels":false,"model_id":"cb4af9c7-41b0-47d3-944a-221446c7b8bc","prompt":"lady","negative_prompt":"(nsfw:0.7), (worst quality:1.5), (low quality:1.5), (normal quality:1.5), lowres,watermark, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, jpeg artifacts, signature, username, blurry, artist name","resolution":{"width":768,"height":1344,"batch_size":1},"model_ability":{"anime_style_control":null},"seed":82879612712,"steps":25,"cfg":7,"sampler_name":"euler_ancestral","scheduler":"karras","ponyTags":{},"denoise":1,"hires_fix_denoise":0.5,"hires_scale":2,"multi_img2img_info":{"style_list":[]},"img_control_info":{"style_list":[]},"continueCreate":false}
返回结构为：{
    "status": 0,
    "message": "success",
    "data": {
        "markId": "d6194183-ac9d-4cb7-ba04-32f73c764e6f",
        "featureName": "ttp",
        "fastHour": true,
        "index": -1
    }
}


有一个POST扩图请求 http://192.168.5.200/api/gen/enlarge-image，application/json，authorization:ca5f728c93fef939e6f23baa34b53ed9aa2768dc, Platform:Web，请求为：{"model_id":"23887bba-507e-4249-a0e3-6951e4027f2b","prompt":"girl","resolution":{"width":1086,"height":1900,"batch_size":1},"enlargeImagePara":{"img_url":"https://uploads.piclumen.com/normal/20250408/19/1e08958316a64bdcafdf04914f784d6e.webp","left":159,"top":278,"right":159,"bottom":278},"continueCreate":false}，
返回结果为：{
    "status": 0,
    "message": "success",
    "data": {
        "markId": "a2bede3b-777e-4ae9-9cf2-1bd936e3726a",
        "featureName": "expand",
        "fastHour": true,
        "index": -1
    }
}

有一个post多图生图请求 http://192.168.5.200/api/gen/multi-edit，application/json，authorization:ca5f728c93fef939e6f23baa34b53ed9aa2768dc, Platform:Web， 请求为：{"model_id":"flux3dc-cff2-4177-ad3a-28d9b4d3ff48","promptId":"00018655675954497479b6ada469852a","prompt":"the girl hug this cat","resolution":{"width":2048,"height":2048,"batch_size":1},"multiImgEditPara":[{"imgUrl":"https://images.piclumen.com/normal/20250828/13/7dced63c007641ffa930f7c542670c6b.webp"},{"imgUrl":"https://images.piclumen.com/temp/20250828/20/72f16ded-3376-45a4-96dd-d7ab8edbfd1d.png"}]}
，其中multiImgEditPara最大size是4
返回结果为：{
    "status": 0,
    "message": "success",
    "data": {
        "markId": "flux-84c8332a-81c4-43a4-a167-d93358d8b690",
        "featureName": "edit",
        "fastHour": true,
        "index": 0
    }



有一个POST查看生图结果的轮询接口：http://192.168.5.200/api/task/batch-process-task，application/json，authorization:ca5f728c93fef939e6f23baa34b53ed9aa2768dc, Platform:Web，请求为：["d6194183-ac9d-4cb7-ba04-32f73c764e6f"]，内容为生图接口返回的markId
返回结果为：{
    "status": 0,
    "message": "success",
    "data": [
        {
            "promptId": "00001054231061518206ea9076b91707",
            "markId": "d6194183-ac9d-4cb7-ba04-32f73c764e6f",
            "status": "success",
            "info": null,
            "index": null,
            "prompt": "a girl",
            "img_urls": [
                {
                    "imgUrl": "https://uploads.piclumen.com/normal/20250408/19/1e08958316a64bdcafdf04914f784d6e.webp",
                    "thumbnailUrl": "https://uploads.piclumen.com/normal/20250408/19/30ef9f26c6764ba299b2ef7ebf824203.webp",
                    "highThumbnailUrl": "https://uploads.piclumen.com/normal/20250408/19/1e08958316a64bdcafdf04914f784d6e.webp",
                    "miniThumbnailUrl": "https://uploads.piclumen.com/normal/20250408/19/d3dee2c724174ad689f93ed5ece48099.webp",
                    "highMiniUrl": "https://uploads.piclumen.com/normal/20250408/19/a0c33f24944f45208382e9324a71666a.webp",
                    "imgName": "normal/20250408/19/1e08958316a64bdcafdf04914f784d6e.webp",
                    "sensitive": null,
                    "realWidth": 768,
                    "realHeight": 1344
                }
            ],
            "featureName": "ttp"
        }
    ]
}