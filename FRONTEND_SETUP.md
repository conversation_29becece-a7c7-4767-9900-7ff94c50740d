# 前端设置和使用指南

## 🎯 前端功能概述

前端已完全重新设计以适配插画生成Agent，提供了：

- **欢迎页面** - 故事输入、风格选择、分镜配置
- **实时进度显示** - 展示生成过程的各个阶段
- **多标签页展示** - 分别展示故事、角色、分镜、图片
- **图片预览和下载** - 支持图片放大查看和下载
- **响应式设计** - 适配不同屏幕尺寸

## 🚀 快速启动

### 1. 安装依赖

```bash
cd frontend
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

前端将在 `http://localhost:5173` 启动。

### 3. 构建生产版本

```bash
npm run build
```

## 🎨 界面功能

### 欢迎页面 (IllustrationWelcome)

- **故事输入区域**
  - 大文本框用于输入故事内容
  - 示例故事快速选择
  - 实时字符计数

- **风格选择**
  - 动漫风格 🎌
  - 写实风格 📸
  - 卡通风格 🎨
  - 赛博朋克 🤖
  - 水彩风格 🎭
  - 素描风格 ✏️

- **分镜配置**
  - 2格漫画 - 简洁展示
  - 4格漫画 - 经典格式
  - 6格漫画 - 详细展示
  - 8格漫画 - 丰富故事

### 生成页面 (IllustrationGenerator)

#### 📊 生成进度标签页
- 实时显示各个生成阶段
- 进度指示器和状态图标
- 详细的阶段描述

#### 📖 故事与角色标签页
- 处理后的故事内容
- 角色信息卡片
- 主角/配角标识
- 角色外貌描述

#### 🎬 分镜设计标签页
- 分镜面板网格布局
- 场景、环境、动作描述
- 氛围和镜头角度信息

#### 🖼️ 生成图片标签页
- 最终合并插画展示
- 单个分镜图片网格
- 图片预览和下载功能
- 生成状态指示

## 🔧 技术特性

### 状态管理
- React Hooks状态管理
- 实时进度更新
- 错误处理和重试机制

### UI组件
- Radix UI组件库
- Tailwind CSS样式
- 响应式设计
- 暗色主题

### API集成
- RESTful API调用
- 错误处理
- 加载状态管理
- 超时处理

## 🧪 测试功能

### API连接测试

创建了 `TestPage` 组件用于测试API连接：

```typescript
// 测试健康检查
GET /api/health

// 测试风格列表
GET /api/styles

// 测试插画生成
POST /api/generate-illustration
```

### 使用测试页面

1. 在 `App.tsx` 中临时替换主组件：

```typescript
import { TestPage } from "@/components/TestPage";

// 在return中使用
<TestPage />
```

2. 启动前端和后端服务
3. 点击测试按钮验证API连接

## 📱 响应式设计

### 桌面端 (≥1024px)
- 多列网格布局
- 侧边栏导航
- 大图片预览

### 平板端 (768px-1023px)
- 两列网格布局
- 折叠式导航
- 中等图片尺寸

### 移动端 (<768px)
- 单列布局
- 底部导航
- 全屏图片预览

## 🎯 用户体验优化

### 加载状态
- 骨架屏加载
- 进度指示器
- 加载动画

### 交互反馈
- 按钮悬停效果
- 点击反馈
- 状态变化动画

### 错误处理
- 友好的错误提示
- 重试机制
- 降级方案

## 🔄 与后端API对接

### 请求格式

```typescript
interface IllustrationRequest {
  user_input: string;        // 用户故事输入
  style_preference: string;  // 艺术风格
  num_panels: number;       // 分镜数量
}
```

### 响应格式

```typescript
interface IllustrationResponse {
  success: boolean;
  final_illustration?: string;    // 最终插画base64
  processed_story?: string;       // 处理后故事
  characters?: Character[];       // 角色信息
  storyboards?: Storyboard[];     // 分镜信息
  generated_images?: Image[];     // 生成图片
  message?: string;              // 结果消息
  error?: string;               // 错误信息
}
```

### API端点

- `GET /api/health` - 健康检查
- `GET /api/styles` - 获取可用风格
- `POST /api/generate-illustration` - 生成插画

## 🚧 开发说明

### 项目结构

```
frontend/src/
├── components/
│   ├── ui/                    # 基础UI组件
│   ├── IllustrationWelcome.tsx # 欢迎页面
│   ├── IllustrationGenerator.tsx # 生成页面
│   └── TestPage.tsx           # 测试页面
├── lib/
│   └── utils.ts              # 工具函数
├── App.tsx                   # 主应用组件
└── main.tsx                  # 应用入口
```

### 添加新功能

1. **新的艺术风格**
   - 在 `IllustrationWelcome.tsx` 的 `STYLE_OPTIONS` 中添加
   - 更新后端API支持

2. **新的分镜选项**
   - 在 `PANEL_OPTIONS` 中添加新选项
   - 调整网格布局逻辑

3. **新的UI组件**
   - 在 `components/ui/` 中添加
   - 遵循现有设计系统

## 🔍 故障排除

### 常见问题

1. **API连接失败**
   - 检查后端服务是否启动
   - 验证API URL配置
   - 检查CORS设置

2. **图片显示问题**
   - 确认图片URL格式正确
   - 检查base64编码
   - 验证图片大小限制

3. **样式问题**
   - 检查Tailwind CSS配置
   - 验证组件导入
   - 确认CSS类名正确

### 调试技巧

1. **使用浏览器开发者工具**
   - Network标签查看API请求
   - Console查看错误信息
   - Elements检查样式

2. **添加调试日志**
   ```typescript
   console.log('API Response:', response);
   console.log('Current State:', state);
   ```

3. **使用React DevTools**
   - 查看组件状态
   - 追踪props传递
   - 性能分析

## 📄 部署说明

### 开发环境
```bash
npm run dev
```

### 生产构建
```bash
npm run build
npm run preview
```

### Docker部署
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 5173
CMD ["npm", "run", "preview"]
```

## 🎉 完成功能

✅ 完全重新设计的UI界面  
✅ 实时进度显示  
✅ 多标签页内容展示  
✅ 图片预览和下载  
✅ 响应式设计  
✅ 错误处理和重试  
✅ API集成和测试  
✅ 用户体验优化  

前端现在已经完全适配插画生成Agent，提供了直观、美观、功能完整的用户界面！
