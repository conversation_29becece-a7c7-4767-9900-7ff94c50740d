# 插画生成Agent使用说明

基于LangGraph构建的智能插画生成系统，能够根据用户提示词自动生成具有故事连续性的插画。

## 🌟 功能特性

- **智能故事处理**: 自动判断输入内容，进行扩写、总结或优化
- **角色一致性**: 提取角色特征，生成基准图保持角色一致性
- **分镜设计**: 将故事拆分为适合插画的分镜描述
- **多风格支持**: 支持动漫、写实、卡通、赛博朋克等多种艺术风格
- **图像生成**: 调用文生图/图生图API生成高质量插画
- **自动合并**: 将多张分镜图合成为完整的插画作品

## 🏗️ 系统架构

```mermaid
flowchart TD
    A[用户输入] --> B[故事处理]
    B --> C[故事分段]
    C --> D[角色提取]
    D --> E[角色基准图生成]
    C --> F[分镜生成]
    E --> F
    F --> G[提示词优化]
    G --> H[图像生成]
    H --> I[图像合并]
    I --> J[最终插画]
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 进入后端目录
cd backend

# 安装依赖
pip install -e .

# 复制环境配置文件
cp .env.example .env
```

### 2. 配置环境变量

编辑 `.env` 文件，配置必要的API密钥：

```env
# Qwen API配置
QWEN_API_KEY=your-qwen-api-key-here
QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# 图像生成API配置
IMAGE_API_URL=http://localhost:8000
IMAGE_API_KEY=your-image-api-key-here

# 默认配置
DEFAULT_STYLE=anime
DEFAULT_NUM_PANELS=4
```

### 3. 启动服务

```bash
# 启动API服务器
python run_server.py
```

服务器将在 `http://localhost:8080` 启动。

### 4. 测试功能

```bash
# 运行测试脚本
python test_illustration_agent.py
```

## 📡 API接口

### 生成插画

**POST** `/api/generate-illustration`

请求体：
```json
{
  "user_input": "小红帽去森林里看望奶奶",
  "style_preference": "anime",
  "num_panels": 4
}
```

响应：
```json
{
  "success": true,
  "final_illustration": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "processed_story": "处理后的故事内容...",
  "characters": [...],
  "storyboards": [...],
  "generated_images": [...],
  "message": "插画生成完成！"
}
```

### 获取可用风格

**GET** `/api/styles`

响应：
```json
{
  "styles": [
    {
      "id": "anime",
      "name": "动漫风格",
      "description": "日式动漫插画风格"
    },
    ...
  ]
}
```

### 健康检查

**GET** `/api/health`

响应：
```json
{
  "status": "healthy",
  "service": "illustration-generation-agent"
}
```

## 🎨 支持的艺术风格

| 风格ID | 名称 | 描述 |
|--------|------|------|
| anime | 动漫风格 | 日式动漫插画风格 |
| realistic | 写实风格 | 真实感强的插画风格 |
| cartoon | 卡通风格 | 可爱的卡通插画风格 |
| cyberpunk | 赛博朋克 | 未来科技感的插画风格 |
| watercolor | 水彩风格 | 柔和的水彩画风格 |
| sketch | 素描风格 | 手绘素描风格 |

## 🔧 配置说明

### LLM配置

系统默认使用Qwen模型，支持以下配置：

- `story_processor_model`: 故事处理模型
- `character_extractor_model`: 角色提取模型
- `storyboard_generator_model`: 分镜生成模型
- `prompt_optimizer_model`: 提示词优化模型

### 图像生成配置

- `image_generation_api_url`: 图像生成API地址
- `image_generation_api_key`: API密钥
- `default_style`: 默认艺术风格
- `default_num_panels`: 默认分镜数量

## 🧪 测试用例

### 基础测试

```python
from src.agent.illustration_graph import generate_illustration_sync

# 简单故事测试
result = generate_illustration_sync(
    user_input="小猫咪迷路了",
    style_preference="anime",
    num_panels=4
)

print(result["final_illustration"])  # 最终插画
```

### API测试

```bash
curl -X POST "http://localhost:8080/api/generate-illustration" \
  -H "Content-Type: application/json" \
  -d '{
    "user_input": "勇敢的骑士拯救公主",
    "style_preference": "anime",
    "num_panels": 4
  }'
```

## 🔍 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `.env` 文件中的API密钥配置
   - 确保Qwen API密钥有效

2. **图像生成失败**
   - 检查图像生成API服务是否正常运行
   - 验证API URL和密钥配置

3. **内存不足**
   - 减少分镜数量 (`num_panels`)
   - 使用更小的图像尺寸

4. **生成速度慢**
   - 检查网络连接
   - 考虑使用本地部署的图像生成服务

### 日志查看

```bash
# 查看详细日志
python run_server.py --log-level debug
```

## 🚧 开发说明

### 项目结构

```
backend/src/agent/
├── app.py                    # FastAPI应用
├── configuration.py          # 配置管理
├── state.py                 # 状态定义
├── illustration_graph.py    # LangGraph工作流
├── illustration_nodes.py    # 节点实现
├── illustration_prompts.py  # 提示词模板
├── llm_utils.py            # LLM工具
├── image_utils.py          # 图像处理工具
└── tools_and_schemas.py    # 数据模式
```

### 扩展功能

1. **添加新的艺术风格**
   - 在 `app.py` 的 `get_available_styles()` 中添加新风格
   - 更新提示词模板以支持新风格

2. **集成新的LLM**
   - 在 `llm_utils.py` 中添加新的LLM初始化函数
   - 更新配置文件

3. **添加新的图像生成服务**
   - 在 `image_utils.py` 中扩展 `ImageGenerationAPI` 类
   - 实现对应的API调用方法

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题，请联系开发团队或查看项目文档。
