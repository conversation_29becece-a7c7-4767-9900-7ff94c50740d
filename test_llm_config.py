#!/usr/bin/env python3
"""
LLM配置测试脚本
"""

import os
import sys
sys.path.insert(0, 'backend/src')

def test_llm_config():
    """测试LLM配置"""
    print("🔧 LLM配置测试")
    print("=" * 40)
    
    # 检查环境变量
    print("📋 环境变量检查:")
    qwen_key = os.getenv("QWEN_API_KEY", "未设置")
    qwen_url = os.getenv("QWEN_BASE_URL", "未设置")
    
    print(f"   QWEN_API_KEY: {qwen_key[:10]}..." if len(qwen_key) > 10 else f"   QWEN_API_KEY: {qwen_key}")
    print(f"   QWEN_BASE_URL: {qwen_url}")
    
    if qwen_key == "未设置" or qwen_key == "your-qwen-api-key":
        print("❌ QWEN_API_KEY 未正确设置")
        return False
    
    # 测试LLM初始化
    try:
        print("\n🤖 LLM初始化测试:")
        from agent.llm_utils import get_default_llm
        
        llm = get_default_llm(temperature=0.1)
        print(f"✅ LLM初始化成功: {type(llm)}")
        
        # 测试简单调用
        print("\n📞 LLM调用测试:")
        response = llm.invoke("请简单回答'测试成功'")
        print(f"✅ LLM调用成功")
        print(f"📝 响应内容: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_workflow_direct():
    """直接测试工作流调用"""
    print("\n🔄 直接工作流测试:")
    print("=" * 40)
    
    try:
        from agent.illustration_graph import generate_illustration
        import asyncio
        
        print("📝 测试简单故事处理...")
        
        async def run_test():
            result = await generate_illustration(
                user_input="一只小猫",
                style_preference="anime",
                num_panels=1,
                progress_callback=lambda n, s, d=None: print(f"进度: {n} -> {s}")
            )
            return result
        
        result = asyncio.run(run_test())
        print(f"✅ 工作流执行成功")
        print(f"📋 结果键: {list(result.keys()) if isinstance(result, dict) else 'Not dict'}")
        
        if 'error' in result:
            print(f"⚠️  工作流返回错误: {result['error']}")
            return False
        else:
            print(f"✅ 工作流执行无错误")
            return True
            
    except Exception as e:
        print(f"❌ 工作流测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🧪 LLM和工作流配置测试")
    print("=" * 50)
    
    # 加载环境变量
    from dotenv import load_dotenv
    load_dotenv('backend/.env')
    
    # 测试LLM配置
    llm_ok = test_llm_config()
    
    # 测试工作流
    workflow_ok = test_workflow_direct()
    
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    print(f"   LLM配置: {'✅ 正常' if llm_ok else '❌ 异常'}")
    print(f"   工作流: {'✅ 正常' if workflow_ok else '❌ 异常'}")
    
    if llm_ok and workflow_ok:
        print("\n🎉 所有测试通过，真实工作流应该能正常执行")
    else:
        print("\n⚠️  存在问题，真实工作流可能无法正常执行")