# 🎨 AI插画生成系统

基于LangGraph构建的智能插画生成系统，能够根据用户提示词自动生成具有故事连续性的插画作品。

![系统架构](agent.png)

## ✨ 功能特性

- 🤖 **智能故事处理** - 自动判断输入内容，进行扩写、总结或优化
- 👥 **角色一致性** - 提取角色特征，生成基准图保持角色外观一致
- 🎬 **智能分镜** - 将故事拆分为适合插画的分镜描述
- 🎨 **多风格支持** - 支持动漫、写实、卡通、赛博朋克等多种艺术风格
- 🖼️ **图像生成** - 调用文生图/图生图API生成高质量插画
- 🔗 **自动合并** - 将多张分镜图合成为完整的插画作品
- 📱 **现代化UI** - 响应式设计，实时进度显示，图片预览下载

## 🏗️ 系统架构

```mermaid
flowchart TD
    A[用户输入] --> B[故事处理]
    B --> C[故事分段]
    C --> D[角色提取]
    D --> E[角色基准图生成]
    C --> F[分镜生成]
    E --> F
    F --> G[提示词优化]
    G --> H[图像生成]
    H --> I[图像合并]
    I --> J[最终插画]
```

## 🚀 快速开始

### 方式一：一键启动（推荐）

```bash
# 使用启动脚本（自动处理所有依赖和配置）
python start_illustration_system.py
```

### 方式二：手动启动

#### 1. 环境准备

**系统要求:**
- Python 3.11+
- Node.js 18+
- npm 或 yarn

#### 2. 后端设置

```bash
# 安装后端依赖
cd backend
pip install -e .

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置API密钥
```

#### 3. 前端设置

```bash
# 安装前端依赖
cd frontend
npm install
```

#### 4. 启动服务

```bash
# 启动后端 (终端1)
cd backend
python run_server.py

# 启动前端 (终端2)
cd frontend
npm run dev
```

### 3. 访问应用

- 🎨 **前端界面**: http://localhost:5173
- 🔧 **后端API**: http://localhost:8080
- 📚 **API文档**: http://localhost:8080/docs

## 🎯 使用指南

### 基本使用流程

1. **输入故事** - 在文本框中输入您的故事内容
2. **选择风格** - 从6种艺术风格中选择
3. **配置分镜** - 选择2-8个分镜数量
4. **生成插画** - 点击生成按钮，等待AI创作
5. **查看结果** - 在多个标签页中查看详细结果

### 支持的艺术风格

| 风格 | 描述 | 适用场景 |
|------|------|----------|
| 🎌 动漫风格 | 日式动漫插画风格 | 二次元故事、轻小说 |
| 📸 写实风格 | 真实感强的插画风格 | 现实题材、纪实故事 |
| 🎨 卡通风格 | 可爱的卡通插画风格 | 儿童故事、轻松内容 |
| 🤖 赛博朋克 | 未来科技感的插画风格 | 科幻故事、未来题材 |
| 🎭 水彩风格 | 柔和的水彩画风格 | 文艺故事、抒情内容 |
| ✏️ 素描风格 | 手绘素描风格 | 简约故事、概念设计 |

## 🔧 配置说明

### 环境变量配置

```env
# Qwen API配置（主要LLM）
QWEN_API_KEY=your-qwen-api-key-here
QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# 图像生成API配置
IMAGE_API_URL=http://localhost:8000
IMAGE_API_KEY=your-image-api-key-here

# 默认配置
DEFAULT_STYLE=anime
DEFAULT_NUM_PANELS=4
```

### API密钥获取

1. **Qwen API密钥**
   - 访问 [阿里云DashScope](https://dashscope.aliyuncs.com/)
   - 注册账号并获取API密钥

2. **图像生成API**
   - 可使用Stable Diffusion、DALL-E等服务
   - 或部署本地图像生成服务

## 📡 API接口

### 生成插画

```bash
curl -X POST "http://localhost:8080/api/generate-illustration" \
  -H "Content-Type: application/json" \
  -d '{
    "user_input": "小红帽去森林里看望奶奶",
    "style_preference": "anime",
    "num_panels": 4
  }'
```

### 获取可用风格

```bash
curl "http://localhost:8080/api/styles"
```

### 健康检查

```bash
curl "http://localhost:8080/api/health"
```

## 🧪 测试

### 运行测试套件

```bash
# 后端测试
cd backend
python test_illustration_agent.py

# 前端API测试
# 在浏览器中访问测试页面或使用curl命令
```

### 示例测试用例

```python
# 简单故事测试
{
  "user_input": "小猫咪迷路了",
  "style_preference": "anime", 
  "num_panels": 4
}

# 复杂故事测试
{
  "user_input": "勇敢的骑士踏上拯救被困公主的冒险之旅...",
  "style_preference": "realistic",
  "num_panels": 6
}
```

## 📁 项目结构

```
插画生成系统/
├── backend/                    # 后端服务
│   ├── src/agent/             # Agent核心代码
│   │   ├── app.py            # FastAPI应用
│   │   ├── illustration_graph.py # LangGraph工作流
│   │   ├── illustration_nodes.py # 节点实现
│   │   ├── illustration_prompts.py # 提示词模板
│   │   ├── llm_utils.py      # LLM工具
│   │   ├── image_utils.py    # 图像处理工具
│   │   └── state.py          # 状态定义
│   ├── test_illustration_agent.py # 测试脚本
│   └── run_server.py         # 服务器启动脚本
├── frontend/                   # 前端应用
│   ├── src/
│   │   ├── components/       # React组件
│   │   │   ├── IllustrationWelcome.tsx # 欢迎页面
│   │   │   ├── IllustrationGenerator.tsx # 生成页面
│   │   │   └── ui/          # UI组件库
│   │   └── App.tsx          # 主应用
│   └── package.json         # 前端依赖
├── start_illustration_system.py # 一键启动脚本
├── ILLUSTRATION_AGENT_README.md # 详细使用说明
├── FRONTEND_SETUP.md        # 前端设置指南
└── PROJECT_SUMMARY.md       # 项目总结
```

## 🔍 故障排除

### 常见问题

1. **API连接失败**
   - 检查后端服务是否启动在8080端口
   - 验证API密钥配置是否正确
   - 检查防火墙和网络设置

2. **图像生成失败**
   - 确认图像生成API服务正常运行
   - 检查API密钥和URL配置
   - 验证网络连接

3. **前端显示异常**
   - 检查Node.js版本是否符合要求
   - 重新安装前端依赖: `npm install`
   - 清除缓存: `npm run build`

### 获取帮助

- 📖 查看详细文档: `ILLUSTRATION_AGENT_README.md`
- 🔧 前端设置指南: `FRONTEND_SETUP.md`
- 📊 项目总结: `PROJECT_SUMMARY.md`
- 🧪 运行测试: `python test_illustration_agent.py`

## 🎉 功能演示

### 输入示例
```
用户输入: "小红帽去森林里看望奶奶，路上遇到了大灰狼"
风格选择: 动漫风格
分镜数量: 4格
```

### 输出结果
- ✅ 处理后的故事（扩写优化）
- ✅ 角色信息（小红帽、大灰狼、奶奶）
- ✅ 4个分镜描述
- ✅ 角色基准图
- ✅ 4张分镜插画
- ✅ 最终合并插画

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

**🎨 开始创作您的专属插画故事吧！**
