#!/usr/bin/env python3
"""
插画生成系统启动脚本
自动启动后端API服务和前端开发服务器
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

class IllustrationSystemLauncher:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True
        
    def check_dependencies(self):
        """检查依赖是否安装"""
        print("🔍 检查系统依赖...")
        
        # 检查Python依赖
        try:
            import uvicorn
            import fastapi
            print("✅ Python依赖检查通过")
        except ImportError as e:
            print(f"❌ Python依赖缺失: {e}")
            print("请运行: cd backend && pip install -e .")
            return False
        
        # 检查Node.js
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Node.js版本: {result.stdout.strip()}")
            else:
                print("❌ Node.js未安装")
                return False
        except FileNotFoundError:
            print("❌ Node.js未找到")
            return False
        
        # 检查npm
        try:
            result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ npm版本: {result.stdout.strip()}")
            else:
                print("❌ npm未安装")
                return False
        except FileNotFoundError:
            print("❌ npm未找到")
            return False
        
        return True
    
    def setup_environment(self):
        """设置环境"""
        print("🔧 设置环境...")
        
        # 检查.env文件
        backend_env = Path("backend/.env")
        if not backend_env.exists():
            print("⚠️ 后端.env文件不存在，从模板创建...")
            env_example = Path("backend/.env.example")
            if env_example.exists():
                import shutil
                shutil.copy(env_example, backend_env)
                print("✅ 已创建.env文件，请配置API密钥")
            else:
                print("❌ .env.example文件不存在")
                return False
        
        # 检查前端依赖
        frontend_node_modules = Path("frontend/node_modules")
        if not frontend_node_modules.exists():
            print("📦 安装前端依赖...")
            try:
                subprocess.run(['npm', 'install'], cwd='frontend', check=True)
                print("✅ 前端依赖安装完成")
            except subprocess.CalledProcessError:
                print("❌ 前端依赖安装失败")
                return False
        
        return True
    
    def start_backend(self):
        """启动后端服务"""
        print("🚀 启动后端API服务...")
        try:
            self.backend_process = subprocess.Popen(
                [sys.executable, 'run_server.py'],
                cwd='backend',
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            print("✅ 后端服务启动中... (http://localhost:8080)")
            return True
        except Exception as e:
            print(f"❌ 后端启动失败: {e}")
            return False
    
    def start_frontend(self):
        """启动前端服务"""
        print("🎨 启动前端开发服务器...")
        try:
            self.frontend_process = subprocess.Popen(
                ['npm', 'run', 'dev'],
                cwd='frontend',
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            print("✅ 前端服务启动中... (http://localhost:5173)")
            return True
        except Exception as e:
            print(f"❌ 前端启动失败: {e}")
            return False
    
    def wait_for_services(self):
        """等待服务启动"""
        print("⏳ 等待服务启动...")
        
        # 等待后端
        backend_ready = False
        for i in range(30):  # 等待30秒
            try:
                import requests
                response = requests.get("http://localhost:8080/api/health", timeout=1)
                if response.status_code == 200:
                    backend_ready = True
                    print("✅ 后端服务就绪")
                    break
            except:
                pass
            time.sleep(1)
            print(f"⏳ 等待后端启动... ({i+1}/30)")
        
        if not backend_ready:
            print("❌ 后端服务启动超时")
            return False
        
        # 等待前端
        frontend_ready = False
        for i in range(20):  # 等待20秒
            try:
                import requests
                response = requests.get("http://localhost:5173", timeout=1)
                if response.status_code == 200:
                    frontend_ready = True
                    print("✅ 前端服务就绪")
                    break
            except:
                pass
            time.sleep(1)
            print(f"⏳ 等待前端启动... ({i+1}/20)")
        
        if not frontend_ready:
            print("⚠️ 前端服务可能需要更多时间启动")
        
        return True
    
    def monitor_services(self):
        """监控服务状态"""
        def monitor_process(process, name):
            while self.running:
                if process and process.poll() is not None:
                    print(f"❌ {name}服务意外停止")
                    break
                time.sleep(5)
        
        # 启动监控线程
        if self.backend_process:
            threading.Thread(target=monitor_process, args=(self.backend_process, "后端"), daemon=True).start()
        
        if self.frontend_process:
            threading.Thread(target=monitor_process, args=(self.frontend_process, "前端"), daemon=True).start()
    
    def show_status(self):
        """显示系统状态"""
        print("\n" + "="*60)
        print("🎉 插画生成系统启动完成！")
        print("="*60)
        print("📍 服务地址:")
        print("   前端界面: http://localhost:5173")
        print("   后端API:  http://localhost:8080")
        print("   API文档:  http://localhost:8080/docs")
        print("\n📋 快速测试:")
        print("   健康检查: curl http://localhost:8080/api/health")
        print("   风格列表: curl http://localhost:8080/api/styles")
        print("\n⌨️ 控制命令:")
        print("   Ctrl+C: 停止所有服务")
        print("   查看日志: 检查终端输出")
        print("="*60)
    
    def cleanup(self):
        """清理资源"""
        print("\n🛑 正在停止服务...")
        self.running = False
        
        if self.backend_process:
            self.backend_process.terminate()
            try:
                self.backend_process.wait(timeout=5)
                print("✅ 后端服务已停止")
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
                print("🔪 强制停止后端服务")
        
        if self.frontend_process:
            self.frontend_process.terminate()
            try:
                self.frontend_process.wait(timeout=5)
                print("✅ 前端服务已停止")
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
                print("🔪 强制停止前端服务")
        
        print("👋 系统已关闭")
    
    def run(self):
        """运行系统"""
        try:
            # 检查依赖
            if not self.check_dependencies():
                return False
            
            # 设置环境
            if not self.setup_environment():
                return False
            
            # 启动服务
            if not self.start_backend():
                return False
            
            if not self.start_frontend():
                self.cleanup()
                return False
            
            # 等待服务就绪
            if not self.wait_for_services():
                self.cleanup()
                return False
            
            # 开始监控
            self.monitor_services()
            
            # 显示状态
            self.show_status()
            
            # 等待用户中断
            try:
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
            
            return True
            
        except Exception as e:
            print(f"❌ 系统启动失败: {e}")
            return False
        finally:
            self.cleanup()

def main():
    """主函数"""
    print("🎨 插画生成系统启动器")
    print("=" * 40)
    
    launcher = IllustrationSystemLauncher()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        launcher.cleanup()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 运行系统
    success = launcher.run()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
