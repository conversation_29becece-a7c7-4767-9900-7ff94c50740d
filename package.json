{"name": "genify", "version": "1.0.0", "description": "基于 LangGraph 和 Google Gemini 模型的全栈研究型 AI 应用", "scripts": {"dev": "npm run dev --prefix frontend", "dev-frontend": "npm run dev --prefix frontend", "dev-backend": "cd backend && python -m agent.app", "build": "npm run build --prefix frontend", "lint": "npm run lint --prefix frontend", "install-deps": "cd frontend && npm install", "clean": "cd frontend && rm -rf node_modules package-lock.json"}, "keywords": ["ai", "research", "langgraph", "gemini"], "author": "", "license": "ISC"}