#!/usr/bin/env python3
"""简单的测试服务器，用于验证SSE实时进度功能"""

import sys
import os
import asyncio
import json
import uuid
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent / "backend" 
sys.path.insert(0, str(backend_dir))

# 模拟设置环境变量
os.environ.setdefault("PYTHONPATH", str(backend_dir))

from fastapi import FastAPI, BackgroundTasks
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import time
import threading
from typing import Dict, List

app = FastAPI(title="测试SSE API服务器")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模拟进度管理器
class MockProgressManager:
    def __init__(self):
        self.sessions = {}
        self.lock = threading.Lock()
    
    def start_session(self, session_id: str):
        with self.lock:
            self.sessions[session_id] = {
                "progress": [],
                "completed": False,
                "error": None,
                "result": None
            }
    
    def update_progress(self, session_id: str, node_name: str, status: str, data=None):
        with self.lock:
            if session_id in self.sessions:
                self.sessions[session_id]["progress"].append({
                    "type": "progress",
                    "node_name": node_name,
                    "status": status,
                    "title": self.get_node_title(node_name),
                    "description": self.get_node_description(node_name, status),
                    "timestamp": time.time(),
                    "data": data
                })
    
    def get_node_title(self, node_name: str) -> str:
        titles = {
            'input_handler': '📝 故事处理',
            'story_splitter': '📚 故事分段', 
            'character_extractor': '👥 角色提取',
            'storyboard_generator': '🎬 分镜生成',
            'scene_prompt_optimizer': '✨ 提示词优化',
            'unified_image_generator': '🖼️ 图像生成',
            'finalize_illustration': '🎉 完成'
        }
        return titles.get(node_name, node_name)
    
    def get_node_description(self, node_name: str, status: str) -> str:
        if status == "in_progress":
            descriptions = {
                'input_handler': '正在分析和优化您的故事内容...',
                'story_splitter': '正在将故事分解为适合插画的分段...',
                'character_extractor': '正在识别故事中的角色并提取特征...',
                'storyboard_generator': '正在创建详细的分镜描述...',
                'scene_prompt_optimizer': '正在优化AI绘画提示词...',
                'unified_image_generator': '正在生成插画图片...',
                'finalize_illustration': '正在最终化插画结果...'
            }
            return descriptions.get(node_name, '处理中...')
        elif status == "completed":
            return f"{self.get_node_title(node_name).split(' ', 1)[1]}完成"
        else:
            return "等待执行..."
    
    def set_completed(self, session_id: str, result: dict):
        with self.lock:
            if session_id in self.sessions:
                self.sessions[session_id]["completed"] = True
                self.sessions[session_id]["result"] = result
    
    def get_progress_updates(self, session_id: str) -> List[dict]:
        with self.lock:
            if session_id in self.sessions:
                progress = self.sessions[session_id]["progress"]
                self.sessions[session_id]["progress"] = []  # 清空已发送的进度
                return progress
        return []
    
    def is_completed(self, session_id: str) -> bool:
        with self.lock:
            return self.sessions.get(session_id, {}).get("completed", False)
    
    def get_final_result(self, session_id: str):
        with self.lock:
            if session_id in self.sessions and self.sessions[session_id]["completed"]:
                return {
                    "type": "result",
                    "success": True,
                    "data": self.sessions[session_id]["result"],
                    "timestamp": time.time()
                }
        return None
    
    def session_exists(self, session_id: str) -> bool:
        with self.lock:
            return session_id in self.sessions

# 全局进度管理器
progress_manager = MockProgressManager()

class IllustrationRequest(BaseModel):
    user_input: str
    style_preference: str = "anime"
    num_panels: int = 4

class GenerationStartResponse(BaseModel):
    success: bool
    session_id: str
    message: str
    error: str = None

@app.post("/api/start-generation", response_model=GenerationStartResponse)
async def start_generation_endpoint(request: IllustrationRequest, background_tasks: BackgroundTasks):
    """启动插画生成的API端点"""
    try:
        session_id = str(uuid.uuid4())
        progress_manager.start_session(session_id)
        
        # 在背景任务中运行模拟生成
        background_tasks.add_task(simulate_generation, session_id, request)
        
        return GenerationStartResponse(
            success=True,
            session_id=session_id,
            message="插画生成已开始"
        )
    except Exception as e:
        return GenerationStartResponse(
            success=False,
            session_id="",
            message="启动失败",
            error=str(e)
        )

@app.get("/api/progress/{session_id}")
async def progress_stream_endpoint(session_id: str):
    """进度流端点，使用Server-Sent Events推送实时进度"""
    if not progress_manager.session_exists(session_id):
        return {"error": "会话ID不存在"}
    
    async def event_stream():
        try:
            while True:
                # 获取进度更新
                progress_data = progress_manager.get_progress_updates(session_id)
                
                if progress_data:
                    for update in progress_data:
                        yield f"data: {json.dumps(update, ensure_ascii=False)}\n\n"
                
                # 检查是否完成
                if progress_manager.is_completed(session_id):
                    final_result = progress_manager.get_final_result(session_id)
                    if final_result:
                        yield f"data: {json.dumps(final_result, ensure_ascii=False)}\n\n"
                    break
                
                await asyncio.sleep(0.5)
                
        except Exception as e:
            error_event = {
                "type": "error",
                "message": f"进度流错误: {str(e)}"
            }
            yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"
    
    return StreamingResponse(
        event_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )

async def simulate_generation(session_id: str, request: IllustrationRequest):
    """模拟插画生成过程"""
    try:
        # 模拟节点执行序列
        nodes = [
            'input_handler',
            'story_splitter', 
            'character_extractor',
            'storyboard_generator',
            'scene_prompt_optimizer',
            'unified_image_generator',
            'finalize_illustration'
        ]
        
        for node in nodes:
            # 开始处理
            progress_manager.update_progress(session_id, node, "in_progress")
            await asyncio.sleep(2)  # 模拟处理时间
            
            # 完成处理
            progress_manager.update_progress(session_id, node, "completed", {
                "node": node,
                "success": True
            })
            await asyncio.sleep(0.5)
        
        # 设置最终结果
        final_result = {
            "final_illustration": "mock://final_illustration.jpg",
            "processed_story": request.user_input,
            "characters": [{"name": "主角", "description": "故事主人公"}],
            "storyboards": [{"panel_id": 1, "description": "第一个分镜"}],
            "generated_images": [{"panel_id": 1, "success": True, "image_url": "mock://image1.jpg"}],
            "messages": [{"content": "插画生成完成！"}]
        }
        
        progress_manager.set_completed(session_id, final_result)
        
    except Exception as e:
        print(f"生成过程出错: {e}")

@app.get("/api/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": "test-illustration-generation-api"}

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动测试SSE API服务器")
    print("📍 地址: http://localhost:8080")
    print("🧪 这是一个测试服务器，用于验证SSE实时进度功能")
    print("=" * 50)
    
    uvicorn.run(app, host="0.0.0.0", port=8080, log_level="info")